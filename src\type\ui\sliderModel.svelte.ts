import { BaseObjectModel } from '../baseObjectModel.svelte';
import type { Script } from '../script.svelte';

export class SliderModel extends BaseObjectModel {

    constructor(slider: any) {
        super(slider);

        // 初始化滑动条特有属性
        this.value = slider.value || 0;
        this.minValue = slider.minValue || 0;
        this.maxValue = slider.maxValue || 100;
        this.step = slider.step || 1;
        // 🔑 子组件绑定属性（所有组件都由外部绑定）
        this.boundRangeSprite = slider.boundRangeSprite || null;       // 范围精灵 (UIImage) - 定义有效范围 ⭐
        this.boundThumbSprite = slider.boundThumbSprite || null;       // 滑块精灵 (UIImage) - 可拖拽控制
        this.boundProgressSprite = slider.boundProgressSprite || null; // 进度精灵 (UIImage) - 显示当前进度
        this.boundLabelSprite = slider.boundLabelSprite || null;       // 文本精灵 (UILabel) - 显示数值

        // 行为属性
        this.enabled = slider.enabled !== false;

        // 事件代码属性
        this.onChangeCode = slider._eventCodes?.onChange || '';
        this.onDragStartCode = slider._eventCodes?.onDragStart || '';
        this.onDragEndCode = slider._eventCodes?.onDragEnd || '';

        // 🔑 编辑器控制属性
        this.executeEventsInEditor = slider.executeEventsInEditor || false;

        // 🔑 统一的脚本系统 - 从原始对象获取脚本数组
        this.componentScripts = slider.componentScripts || [];

        // 🔑 如果没有脚本，添加默认脚本
        if (this.componentScripts.length === 0) {
            this.componentScripts = [{
                id: 'default',
                name: 'Default Script',
                type: 'lifecycle',
                enabled: true,
                code: `// UISlider 默认脚本
function onStart() {
    console.log("滑块启动:", self.name);
}

function onUpdate() {
    // 每帧更新逻辑
}

function onDestroy() {
    console.log("滑块销毁:", self.name);
    // 在这里添加清理逻辑
}

// 🔑 数据事件
function onChange(newValue, oldValue) {
    console.log(\`滑块值从 \${oldValue} 改变为 \${newValue}\`);
}

// 🔑 交互事件
function onSliderStart(currentValue) {
    console.log("开始拖拽滑块，当前值:", currentValue);
}

function onSliderEnd(currentValue) {
    console.log("结束拖拽滑块，当前值:", currentValue);
}`,
                description: 'UISlider 默认生命周期脚本'
            }];
        }

        console.log('🔧 SliderModel: 创建滑动条模型', slider);

        // setupSync() 已经在基类构造函数中调用了

        // 🔑 延迟重建绑定关系（等待所有子对象加载完成）
        setTimeout(() => this.rebuildBindingsFromChildren(), 0);
    }

    // 滑动条数值属性
    value = $state(0);              // 当前值
    minValue = $state(0);           // 最小值
    maxValue = $state(100);         // 最大值
    step = $state(1);               // 步长



    // 🔑 子组件绑定属性（所有组件都由外部绑定）- 存储模型对象
    boundRangeSprite = $state<BaseObjectModel | null>(null);     // 绑定的范围精灵模型 (UIImage) - 定义有效范围 ⭐
    boundThumbSprite = $state<BaseObjectModel | null>(null);     // 绑定的滑块精灵模型 (UIImage) - 可拖拽控制
    boundProgressSprite = $state<BaseObjectModel | null>(null);  // 绑定的进度精灵模型 (UIImage) - 显示当前进度
    boundLabelSprite = $state<BaseObjectModel | null>(null);     // 绑定的文本精灵模型 (UILabel) - 显示数值

    // 行为属性
    enabled = $state(true);          // 是否启用

    // 事件代码属性
    onChangeCode = $state('');       // 值改变事件代码
    onDragStartCode = $state('');    // 开始拖拽事件代码
    onDragEndCode = $state('');      // 结束拖拽事件代码

    // 🔑 统一的脚本系统
    componentScripts = $state<Script[]>([]);   // 脚本数组

    // 🔑 编辑器控制属性
    executeEventsInEditor = $state(false);     // 是否在编辑器中执行事件

    /**
     * 🔑 从子对象重建绑定关系（用于加载保存的场景）
     * 根据子对象的类型自动识别并重建绑定关系
     */
    rebuildBindingsFromChildren(): void {
        console.log('🔄 SliderModel: 开始重建绑定关系', {
            childrenCount: this.children.length,
            children: this.children.map(child => ({
                className: child.className,
                name: child.name
            }))
        });

        // 清空现有绑定
        this.boundRangeSprite = null;
        this.boundThumbSprite = null;
        this.boundProgressSprite = null;
        this.boundLabelSprite = null;

        // 遍历子对象，根据类型重建绑定
        for (const child of this.children) {
            if (child.className === 'UIImage') {
                // 根据位置和尺寸推断UIImage的用途
                if (!this.boundRangeSprite) {
                    // 第一个UIImage作为范围精灵
                    this.boundRangeSprite = child;
                    console.log('🔗 重建绑定: 范围精灵', child.name);
                } else if (!this.boundProgressSprite) {
                    // 第二个UIImage作为进度精灵
                    this.boundProgressSprite = child;
                    console.log('🔗 重建绑定: 进度精灵', child.name);
                } else if (!this.boundThumbSprite) {
                    // 第三个UIImage作为滑块精灵
                    this.boundThumbSprite = child;
                    console.log('🔗 重建绑定: 滑块精灵', child.name);
                }
            } else if (child.className === 'UILabel') {
                // UILabel作为文本精灵
                if (!this.boundLabelSprite) {
                    this.boundLabelSprite = child;
                    console.log('🔗 重建绑定: 文本精灵', child.name);
                }
            }
        }

        console.log('✅ SliderModel: 绑定关系重建完成', {
            boundRangeSprite: this.boundRangeSprite?.name || 'null',
            boundThumbSprite: this.boundThumbSprite?.name || 'null',
            boundProgressSprite: this.boundProgressSprite?.name || 'null',
            boundLabelSprite: this.boundLabelSprite?.name || 'null'
        });
    }

    /**
     * 设置Slider特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Slider特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步滑动条特有属性
        if (this._originalObject.setValue && typeof this._originalObject.setValue === 'function') {
            this._originalObject.setValue(this.value);
        } else {
            this._originalObject.value = this.value;
        }

        this._originalObject.minValue = this.minValue;
        this._originalObject.maxValue = this.maxValue;
        this._originalObject.step = this.step;

        // 🔑 绑定范围精灵：从模型对象获取原始对象
        if (this.boundRangeSprite) {
            // 🔧 安全检查：确保绑定对象有getOriginalObject方法
            if (typeof this.boundRangeSprite.getOriginalObject === 'function') {
                const originalRangeSprite = this.boundRangeSprite.getOriginalObject();
                if (this._originalObject.bindRangeSprite) {
                    this._originalObject.bindRangeSprite(originalRangeSprite);
                } else {
                    this._originalObject.boundRangeSprite = originalRangeSprite;
                }
            } else {
                console.warn('🚨 SliderModel: boundRangeSprite 不是有效的模型对象，跳过绑定');
                this._originalObject.boundRangeSprite = null;
            }
        } else {
            this._originalObject.boundRangeSprite = null;
        }

        // 🔑 绑定滑块精灵：从模型对象获取原始对象
        if (this.boundThumbSprite) {
            // 🔧 安全检查：确保绑定对象有getOriginalObject方法
            if (typeof this.boundThumbSprite.getOriginalObject === 'function') {
                const originalThumbSprite = this.boundThumbSprite.getOriginalObject();
                if (this._originalObject.bindThumbSprite) {
                    this._originalObject.bindThumbSprite(originalThumbSprite);
                } else {
                    this._originalObject.boundThumbSprite = originalThumbSprite;
                }
            } else {
                console.warn('🚨 SliderModel: boundThumbSprite 不是有效的模型对象，跳过绑定');
                this._originalObject.boundThumbSprite = null;
            }
        } else {
            this._originalObject.boundThumbSprite = null;
        }

        // 🔑 绑定进度精灵：从模型对象获取原始对象
        if (this.boundProgressSprite) {
            // 🔧 安全检查：确保绑定对象有getOriginalObject方法
            if (typeof this.boundProgressSprite.getOriginalObject === 'function') {
                const originalProgressSprite = this.boundProgressSprite.getOriginalObject();
                if (this._originalObject.bindProgressSprite) {
                    this._originalObject.bindProgressSprite(originalProgressSprite);
                } else {
                    this._originalObject.boundProgressSprite = originalProgressSprite;
                }
            } else {
                console.warn('🚨 SliderModel: boundProgressSprite 不是有效的模型对象，跳过绑定');
                this._originalObject.boundProgressSprite = null;
            }
        } else {
            this._originalObject.boundProgressSprite = null;
        }

        // 🔑 绑定文本精灵：从模型对象获取原始对象
        if (this.boundLabelSprite) {
            // 🔧 安全检查：确保绑定对象有getOriginalObject方法
            if (typeof this.boundLabelSprite.getOriginalObject === 'function') {
                const originalLabelSprite = this.boundLabelSprite.getOriginalObject();
                if (this._originalObject.bindLabelSprite) {
                    this._originalObject.bindLabelSprite(originalLabelSprite);
                } else {
                    this._originalObject.boundLabelSprite = originalLabelSprite;
                }
            } else {
                console.warn('🚨 SliderModel: boundLabelSprite 不是有效的模型对象，跳过绑定');
                this._originalObject.boundLabelSprite = null;
            }
        } else {
            this._originalObject.boundLabelSprite = null;
        }

        // 同步行为属性
        if (this._originalObject.setEnabled && typeof this._originalObject.setEnabled === 'function') {
            this._originalObject.setEnabled(this.enabled);
        } else {
            this._originalObject.enabled = this.enabled;
        }

        // orientation属性已删除

        // 同步事件代码属性
        if (!this._originalObject._eventCodes) {
            this._originalObject._eventCodes = {};
        }
        this._originalObject._eventCodes.onChange = this.onChangeCode;
        this._originalObject._eventCodes.onDragStart = this.onDragStartCode;
        this._originalObject._eventCodes.onDragEnd = this.onDragEndCode;

        // 🔑 同步脚本系统
        this._originalObject.componentScripts = this.componentScripts;

        // 🔑 同步编辑器控制属性
        this._originalObject.executeEventsInEditor = this.executeEventsInEditor;
    }

    /**
     * 设置滑动条值
     */
    public setValue(value: number): void {
        // 限制范围
        value = Math.max(this.minValue, Math.min(this.maxValue, value));

        // 应用步长
        if (this.step > 0) {
            value = Math.round((value - this.minValue) / this.step) * this.step + this.minValue;
        }

        this.value = value;
    }

    /**
     * 设置滑动条范围
     */
    public setRange(minValue: number, maxValue: number): void {
        this.minValue = minValue;
        this.maxValue = maxValue;
        // 重新验证当前值
        this.setValue(this.value);
    }

    /**
     * 获取进度比例 (0-1)
     */
    public getProgress(): number {
        if (this.maxValue === this.minValue) return 0;
        return (this.value - this.minValue) / (this.maxValue - this.minValue);
    }

    /**
     * 获取绑定状态信息
     */
    public getBindingInfo(): {
        hasRange: boolean;
        hasThumb: boolean;
        hasProgress: boolean;
        hasLabel: boolean;
        rangeSprite?: any;
        thumbSprite?: any;
        progressSprite?: any;
        labelSprite?: any;
    } {
        return {
            hasRange: this.boundRangeSprite !== null,
            hasThumb: this.boundThumbSprite !== null,
            hasProgress: this.boundProgressSprite !== null,
            hasLabel: this.boundLabelSprite !== null,
            rangeSprite: this.boundRangeSprite,
            thumbSprite: this.boundThumbSprite,
            progressSprite: this.boundProgressSprite,
            labelSprite: this.boundLabelSprite
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建UISlider容器对象
        codes.push(`${indent}const ${varName} = new UISlider({`);
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);
        codes.push(`${indent}    value: ${this.value},`);
        codes.push(`${indent}    minValue: ${this.minValue},`);
        codes.push(`${indent}    maxValue: ${this.maxValue},`);
        codes.push(`${indent}    step: ${this.step},`);
        codes.push(`${indent}    enabled: ${this.enabled}`);
        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns Slider特定属性设置代码
     */
    protected generateSpecificProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 不在这里生成绑定代码，因为子对象还没创建
        // 绑定代码将在 generateBindingCode() 中生成

        // 事件代码
        if (this.onChangeCode) {
            codes.push(`${indent}// 值改变事件`);
            codes.push(`${indent}${varName}._eventCodes.onChange = '${this.onChangeCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onDragStartCode) {
            codes.push(`${indent}// 开始拖拽事件`);
            codes.push(`${indent}${varName}._eventCodes.onDragStart = '${this.onDragStartCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onDragEndCode) {
            codes.push(`${indent}// 结束拖拽事件`);
            codes.push(`${indent}${varName}._eventCodes.onDragEnd = '${this.onDragEndCode.replace(/'/g, "\\'")}';`);
        }

        return codes.join('\n');
    }

    /**
     * 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成绑定代码（在子对象创建之后调用）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 绑定代码
     */
    protected generateBindingCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 辅助函数：查找绑定对象对应的子对象变量名
        const findChildVarName = (boundObject: any): string | null => {
            if (!boundObject) return null;

            // 🔑 现在boundObject是模型对象，需要直接比较模型对象
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child === boundObject) {
                    return `${varName}_child${i}`;
                }
            }
            return null;
        };

        // 只有当有绑定对象时才添加注释和绑定代码
        let hasBindings = false;

        // 检查是否有任何绑定
        if (this.boundRangeSprite || this.boundThumbSprite ||
            this.boundProgressSprite || this.boundLabelSprite) {
            codes.push(`${indent}// 绑定子组件`);
            hasBindings = true;
        }

        // 绑定子组件（如果有的话）- boundTrackSprite已删除

        if (this.boundRangeSprite) {
            const childVarName = findChildVarName(this.boundRangeSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定范围精灵 (UIImage) - 定义有效范围`);
                codes.push(`${indent}${varName}.bindRangeSprite(${childVarName});`);
            }
        }

        if (this.boundThumbSprite) {
            const childVarName = findChildVarName(this.boundThumbSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定滑块精灵 (UIImage) - 可拖拽控制`);
                codes.push(`${indent}${varName}.bindThumbSprite(${childVarName});`);
            }
        }

        if (this.boundProgressSprite) {
            const childVarName = findChildVarName(this.boundProgressSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定进度精灵 (UIImage) - 显示当前进度`);
                codes.push(`${indent}${varName}.bindProgressSprite(${childVarName});`);
            }
        }

        if (this.boundLabelSprite) {
            const childVarName = findChildVarName(this.boundLabelSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定文本精灵 (UILabel) - 显示数值`);
                codes.push(`${indent}${varName}.bindLabelSprite(${childVarName});`);
            }
        }

        return codes.join('\n');
    }

    /**
     * 克隆当前Slider对象 - 调用插件的 clone 方法
     */
    clone(): SliderModel {
        console.log('🔄 SliderModel: 开始克隆Slider对象（调用插件方法）');

        // 1. 调用原始 UISlider 对象的 clone 方法
        const originalUISlider = this.getOriginalObject();
        if (!originalUISlider || typeof originalUISlider.clone !== 'function') {
            console.error('❌ SliderModel: 原始对象没有 clone 方法');
            throw new Error('UISlider 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUISlider = originalUISlider.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 SliderModel 包装克隆的对象
        const clonedModel = new SliderModel(clonedUISlider);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ SliderModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 获取子对象的变量名
     */
    private getChildVariableName(childObject: any, parentVarName: string): string {
        // 🔑 现在childObject是模型对象，需要直接比较模型对象
        const childIndex = this.children.findIndex(child => child === childObject);
        if (childIndex !== -1) {
            return `${parentVarName}_child${childIndex}`;
        }

        // 🔑 如果找不到，说明绑定的对象不在children中，这是错误的
        console.error('🚨 SliderModel: 无法找到绑定对象的变量名', {
            childObject: childObject?.className,
            parentVarName,
            childrenCount: this.children.length
        });
        return `/* ERROR: 找不到绑定对象 ${childObject?.className || 'unknown'} */`;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UISlider 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UISlider 特有属性
            minValue: this.minValue,
            maxValue: this.maxValue,
            value: this.value,
            step: this.step,
            sliderWidth: this.sliderWidth,
            sliderHeight: this.sliderHeight,

            // 事件代码
            _eventCodes: {
                onChange: this.onChangeCode
            },

            // 🔑 统一的脚本系统
            componentScripts: this.componentScripts
        };
    }

    /**
     * 🔑 重写对象创建代码生成（参考 UILabel）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 创建 UISlider 对象
        codes.push(`${indent}const ${varName} = new UISlider({`);

        // 基础属性
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);
        codes.push(`${indent}    minValue: ${this.minValue},`);
        codes.push(`${indent}    maxValue: ${this.maxValue},`);
        codes.push(`${indent}    value: ${this.value},`);
        codes.push(`${indent}    step: ${this.step},`);
        codes.push(`${indent}    sliderWidth: ${this.sliderWidth},`);
        codes.push(`${indent}    sliderHeight: ${this.sliderHeight},`);

        // 🔑 编辑器控制属性
        if (this.executeEventsInEditor) {
            codes.push(`${indent}    executeEventsInEditor: ${this.executeEventsInEditor},`);
        }

        // 🔑 生成组件脚本数组（如果有的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            codes.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, `\n${indent}`)},`);
        }

        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 🔑 检查是否有非空的脚本内容（参考 UILabel）
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

}

// 注册SliderModel到基类容器
BaseObjectModel.registerModel('UISlider', SliderModel);
BaseObjectModel.registerModel('Slider', SliderModel);
