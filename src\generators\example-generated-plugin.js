/*:
 * @target MZ
 * @plugindesc RPGEditor_GeneratedPlugin v1.0.0
 * <AUTHOR> Editor
 * @version 1.0.0
 * @description Auto-generated plugin from RPG Editor
 *
 * @help RPGEditor_GeneratedPlugin.js
 *
 * This plugin was automatically generated by RPG Editor.
 * It recreates the scene objects and UI elements based on saved data.
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 * Free for commercial and non-commercial use.
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 * Version 1.0.0: Initial release
 */

// ===== 插件代码立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始加载插件代码");

  // ===== uiScriptManager.js =====
  // 插件代码内容...
  
  // ===== uiComponent.js =====
  // 插件代码内容...
  
  // ===== 其他插件文件... =====

  console.log("RPG Editor: 插件代码加载完成");
})();

// ===== Scene_Title 场景立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始处理 Scene_Title");

  // Scene_Title 场景创建方法
  Scene_Title.prototype.create = function() {
    Scene_Base.prototype.create.call(this);
    console.log('RPG Editor: 开始创建 Scene_Title 的自定义对象');

    // 添加子对象
    const obj_Sprite_0 = new Sprite({
      bitmap: {
        url: 'img/titles1/Castle.png'
      }
    });
    obj_Sprite_0.x = 0;
    obj_Sprite_0.y = 0;
    this.addChild(obj_Sprite_0);

    console.log('RPG Editor: Scene_Title 自定义对象创建完成');
  };

  // ===== Scene_Title 方法重写 =====
  // 重写 createBackground - 跳过原生背景创建
  Scene_Title.prototype.createBackground = function() {
    console.log('RPG Editor: 跳过原生背景创建，使用编辑器对象');
  };

  // 重写 createForeground - 跳过原生前景创建
  Scene_Title.prototype.createForeground = function() {
    console.log('RPG Editor: 跳过原生前景创建，使用编辑器对象');
  };

  // 重写 createCommandWindow - 跳过原生命令窗口创建
  Scene_Title.prototype.createCommandWindow = function() {
    console.log('RPG Editor: 跳过原生命令窗口创建，使用编辑器对象');
  };

  console.log("RPG Editor: Scene_Title 处理完成");
})();

// ===== Scene_Map 场景立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始处理 Scene_Map");

  // Scene_Map 场景创建方法
  Scene_Map.prototype.create = function() {
    Scene_Base.prototype.create.call(this);
    console.log('RPG Editor: 开始创建 Scene_Map 的自定义对象');

    // 添加子对象
    const obj_UIButton_0 = new UIButton({
      text: '开始游戏',
      x: 100,
      y: 200
    });
    this.addChild(obj_UIButton_0);

    console.log('RPG Editor: Scene_Map 自定义对象创建完成');
  };

  // ===== Scene_Map 方法重写 =====
  // 重写 createSpriteset - 跳过原生精灵集创建
  Scene_Map.prototype.createSpriteset = function() {
    console.log('RPG Editor: 跳过原生精灵集创建，使用编辑器对象');
  };

  // 重写 createAllWindows - 跳过原生窗口创建
  Scene_Map.prototype.createAllWindows = function() {
    console.log('RPG Editor: 跳过原生窗口创建，使用编辑器对象');
  };

  console.log("RPG Editor: Scene_Map 处理完成");
})();
