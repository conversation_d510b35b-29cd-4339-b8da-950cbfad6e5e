<script lang="ts">
  // API侧边栏组件
  export let onCodeInsert: (code: string) => void = () => {};

  // 导入RPG数据
  import rpgData from "./rpgData";

  // 从新的模块化结构中获取数据
  const { dataObjects, gameObjects, managerObjects, examples, objectExamples } = rpgData;

  // 侧边栏状态
  let activeTab: "ui" | "rpg" | "scenes" = "ui";
  let rpgSubTab: "properties" | "methods" | "examples" = "properties";
  let selectedObject: string = "$dataActors"; // 当前选中的RPG对象

  // 自定义下拉框状态
  let dropdownOpen = false;

  // 场景列表数据
  const sceneList = [
    { name: "Scene_Boot", description: "启动场景" },
    { name: "Scene_Title", description: "标题场景" },
    { name: "Scene_Map", description: "地图场景" },
    { name: "Scene_Menu", description: "菜单场景" },
    { name: "Scene_Item", description: "物品场景" },
    { name: "Scene_Skill", description: "技能场景" },
    { name: "Scene_Equip", description: "装备场景" },
    { name: "Scene_Status", description: "状态场景" },
    { name: "Scene_Options", description: "选项场景" },
    { name: "Scene_File", description: "文件场景" },
    { name: "Scene_Save", description: "保存场景" },
    { name: "Scene_Load", description: "读取场景" },
    { name: "Scene_GameEnd", description: "游戏结束场景" },
    { name: "Scene_Shop", description: "商店场景" },
    { name: "Scene_Name", description: "命名场景" },
    { name: "Scene_Debug", description: "调试场景" },
    { name: "Scene_Battle", description: "战斗场景" },
    { name: "Scene_Gameover", description: "游戏结束场景" },
  ];

  // UI组件数据
  const uiComponents = [
    {
      name: "GlobAPI",
      description: "全局api",
      items: [
        {
          name: "watchData",
          type: "property",
          description: "监听数据变化",
          code: "watchData('$gameParty._gold', (newValue, oldValue, key) => {\n  console.log('数据变化:', newValue);\n  // key 参数在监听对象时可用\n});",
        },
      ],
    },
    {
      name: "UILabel",
      description: "文本标签组件",
      items: [
        {
          name: "text",
          type: "property",
          description: "显示的文本内容",
          code: 'label.text = "新文本";',
        },
      ],
    },
    {
      name: "UIImage",
      description: "图片标签组件",
      items: [
        {
          name: "src",
          type: "property",
          description: "显示的图片内容",
          code: 'this.src = "img/";',
        },
        {
          name: "setFace",
          type: "property",
          description: "设置角色脸图",
          code: 'this.setFace("Actor1", 0)',
        },
      ],
    },
    {
      name: "UILayout",
      description: "布局组件",
      items: [
        {
          name: "setTemplateData",
          type: "property",
          description: "设置模板数据",
          code: "this.setTemplateData(ctx);",
        },
      ],
    },
    {
      name: "UISwitch",
      description: "开关组件",
      items: [
        {
          name: "value",
          type: "property",
          description: "设置开关值",
          code: "this.value = true;",
        },
      ],
    },
    {
      name: "UISlider",
      description: "滑动条组件",
      items: [
        {
          name: "value",
          type: "property",
          description: "设置滑动条值",
          code: "this.value = true;",
        },
      ],
    },
    {
      name: "UIButton",
      description: "按钮组件",
      // items: [
      //   { name: 'text', type: 'property', description: '按钮文本', code: 'button.text = "确定";' },
      //   { name: 'enabled', type: 'property', description: '是否启用', code: 'button.enabled = false;' },
      //   { name: 'setEnabled', type: 'method', description: '设置启用状态', code: 'button.setEnabled(true);' }
      // ]
    },
    {
      name: "UIMask",
      description: "遮罩容器组件",
      // items: [
      //   { name: 'maskType', type: 'property', description: '遮罩类型', code: 'mask.maskType = "circle";' },
      //   { name: 'width', type: 'property', description: '遮罩宽度', code: 'mask.width = 300;' },
      //   { name: 'height', type: 'property', description: '遮罩高度', code: 'mask.height = 200;' },
      //   { name: 'updateSize', type: 'method', description: '更新遮罩尺寸', code: 'mask.updateSize(400, 300);' }
      // ]
    },
  ];

  // 处理项目点击
  function handleItemClick(code: string) {
    onCodeInsert(code);
  }

  // 自定义下拉框函数
  function toggleDropdown() {
    dropdownOpen = !dropdownOpen;
  }

  function selectObject(objName: string) {
    selectedObject = objName;
    dropdownOpen = false;
  }

  // 获取当前选中对象的显示名称
  function getSelectedObjectDisplay() {
    const allObjects = [...dataObjects, ...gameObjects, ...managerObjects];
    const obj = allObjects.find(o => o.name === selectedObject);
    return obj ? `${obj.name} - ${obj.description}` : selectedObject;
  }
</script>

<div class="api-sidebar">
  <!-- 侧边栏头部 -->
  <div class="sidebar-header">
    <h3>API 参考</h3>
  </div>

  <!-- 主标签页 -->
  <div class="main-tabs">
    <button
      class="tab-btn {activeTab === 'ui' ? 'active' : ''}"
      on:click={() => (activeTab = "ui")}
    >
      UI组件
    </button>
    <button
      class="tab-btn {activeTab === 'rpg' ? 'active' : ''}"
      on:click={() => (activeTab = "rpg")}
    >
      RPG对象
    </button>
    <button
      class="tab-btn {activeTab === 'scenes' ? 'active' : ''}"
      on:click={() => (activeTab = "scenes")}
    >
      场景
    </button>
  </div>

  <!-- RPG子标签页 -->
  {#if activeTab === "rpg"}
    <div class="sub-tabs">
      <button
        class="sub-tab-btn {rpgSubTab === 'properties' ? 'active' : ''}"
        on:click={() => (rpgSubTab = "properties")}
      >
        属性
      </button>
      <button
        class="sub-tab-btn {rpgSubTab === 'methods' ? 'active' : ''}"
        on:click={() => (rpgSubTab = "methods")}
      >
        方法
      </button>
      <button
        class="sub-tab-btn {rpgSubTab === 'examples' ? 'active' : ''}"
        on:click={() => (rpgSubTab = "examples")}
      >
        示例
      </button>
    </div>
  {/if}

  <!-- 内容区域 -->
  <div class="content-area">
    {#if activeTab === "ui"}
      <!-- UI组件内容 -->
      {#each uiComponents as component}
        <div class="component-section">
          <div class="component-title">{component.name}</div>
          <div class="component-desc">{component.description}</div>

          {#each component.items as item}
            <div
              class="api-item {item.type}"
              role="button"
              tabindex="0"
              on:click={() => handleItemClick(item.code)}
              on:keydown={(e) =>
                e.key === "Enter" && handleItemClick(item.code)}
            >
              <span class="item-name">{item.name}</span>
              <span class="item-desc">{item.description}</span>
            </div>
          {/each}
        </div>
      {/each}
    {:else if activeTab === "rpg"}
      <!-- RPG对象内容 -->
      <div class="rpg-section">
        <!-- 自定义对象选择器 -->
        <div class="object-selector">
          <div class="custom-select" on:click={toggleDropdown}>
            <div class="select-display">
              {getSelectedObjectDisplay()}
            </div>
            <div class="select-arrow">▼</div>
          </div>

          {#if dropdownOpen}
            <div class="dropdown-list">
              <div class="dropdown-group">
                <div class="group-title">数据对象</div>
                {#each dataObjects as obj}
                  <div class="dropdown-item" on:click={() => selectObject(obj.name)}>
                    {obj.name} - {obj.description}
                  </div>
                {/each}
              </div>

              <div class="dropdown-group">
                <div class="group-title">游戏对象</div>
                {#each gameObjects as obj}
                  <div class="dropdown-item" on:click={() => selectObject(obj.name)}>
                    {obj.name} - {obj.description}
                  </div>
                {/each}
              </div>

              <div class="dropdown-group">
                <div class="group-title">管理器对象</div>
                {#each managerObjects as obj}
                  <div class="dropdown-item" on:click={() => selectObject(obj.name)}>
                    {obj.name} - {obj.description}
                  </div>
                {/each}
              </div>
            </div>
          {/if}
        </div>

        <!-- 显示选中对象的属性或方法 -->
        {#each [...dataObjects, ...gameObjects, ...managerObjects] as obj}
          {#if obj.name === selectedObject}
            {#if rpgSubTab === "properties"}
              <div class="section-title">属性</div>
              {#each obj.properties as property}
                <div
                  class="api-item property"
                  role="button"
                  tabindex="0"
                  on:click={() => handleItemClick(property.code)}
                  on:keydown={(e) =>
                    e.key === "Enter" && handleItemClick(property.code)}
                >
                  <span class="item-name">{property.name}</span>
                  <span class="item-desc">{property.description}</span>
                </div>
              {/each}
            {:else if rpgSubTab === "methods"}
              <div class="section-title">方法</div>
              {#each obj.methods as method}
                <div
                  class="api-item method"
                  role="button"
                  tabindex="0"
                  on:click={() => handleItemClick(method.code)}
                  on:keydown={(e) =>
                    e.key === "Enter" && handleItemClick(method.code)}
                >
                  <span class="item-name">{method.name}</span>
                  <span class="item-desc">{method.description}</span>
                </div>
              {/each}
            {:else if rpgSubTab === "examples"}
              <div class="section-title">示例</div>
              {#if objectExamples[obj.name]}
                {#each objectExamples[obj.name] as example}
                  <div class="example-card">
                    <div class="example-header">
                      <h4 class="example-title">{example.name}</h4>
                      <p class="example-description">{example.description}</p>
                    </div>
                    <div class="example-code">
                      <button
                        class="copy-code-btn"
                        on:click={() => handleItemClick(example.code)}
                        title="复制代码到编辑器"
                      >
                        📋 复制代码
                      </button>
                      <pre><code>{example.code}</code></pre>
                    </div>
                  </div>
                {/each}
              {:else}
                <div class="no-examples">暂无示例</div>
              {/if}
            {/if}
          {/if}
        {/each}
      </div>
    {:else if activeTab === "scenes"}
      <!-- 场景列表内容 -->
      <div class="scenes-section">
        <div class="section-title">RPG Maker MZ 场景</div>
        {#each sceneList as scene}
          <div
            class="api-item scene"
            role="button"
            tabindex="0"
            on:click={() => handleItemClick(scene.name)}
            on:keydown={(e) => e.key === "Enter" && handleItemClick(scene.name)}
          >
            <span class="item-name">{scene.name}</span>
            <span class="item-desc">{scene.description}</span>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div>

<style>
  .api-sidebar {
    width: 100%;
    height: 100%;
    background: #2d2d2d;
    border-left: 1px solid #404040;
    display: flex;
    flex-direction: column;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    /* 强制限制整个侧边栏的显示范围 */
    overflow: hidden;
    position: relative;
  }

  .sidebar-header {
    padding: 12px 16px;
    border-bottom: 1px solid #404040;
    background: #252525;
  }

  .sidebar-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
  }

  .main-tabs {
    display: flex;
    border-bottom: 1px solid #404040;
    background: #2a2a2a;
  }

  .tab-btn {
    flex: 1;
    padding: 10px 12px;
    background: transparent;
    border: none;
    color: #cccccc;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .tab-btn:hover {
    background: #3a3a3a;
    color: #ffffff;
  }

  .tab-btn.active {
    background: #007acc;
    color: #ffffff;
  }

  .sub-tabs {
    display: flex;
    border-bottom: 1px solid #404040;
    background: #252525;
  }

  .sub-tab-btn {
    flex: 1;
    padding: 8px 10px;
    background: transparent;
    border: none;
    color: #aaaaaa;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .sub-tab-btn:hover {
    background: #333333;
    color: #cccccc;
  }

  .sub-tab-btn.active {
    background: #005a9e;
    color: #ffffff;
  }

  .content-area {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .component-section,
  .rpg-section {
    margin-bottom: 16px;
  }

  .component-title,
  .section-title {
    font-size: 13px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 4px;
    padding: 4px 8px;
    background: #404040;
    border-radius: 3px;
  }

  .component-desc {
    font-size: 11px;
    color: #aaaaaa;
    margin-bottom: 8px;
    padding: 0 8px;
  }

  .api-item {
    padding: 6px 8px;
    margin: 2px 0;
    background: #333333;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
    border-left: 3px solid transparent;
  }

  .api-item:hover {
    background: #3a3a3a;
    border-left-color: #007acc;
  }

  .api-item.property {
    border-left-color: #4caf50;
  }

  .api-item.method {
    border-left-color: #ff9800;
  }

  .api-item.event {
    border-left-color: #9c27b0;
  }

  .api-item.example {
    border-left-color: #e91e63;
  }

  .no-examples {
    padding: 8px;
    color: #888888;
    font-style: italic;
    text-align: center;
  }

  /* 示例卡片样式 */
  .example-card {
    background: #2a2a2a;
    border: 1px solid #404040;
    border-radius: 6px;
    margin-bottom: 12px;
    overflow: hidden;
  }

  .example-header {
    padding: 12px;
    background: #333333;
    border-bottom: 1px solid #404040;
  }

  .example-title {
    margin: 0 0 6px 0;
    font-size: 14px;
    font-weight: 600;
    color: #e91e63;
  }

  .example-description {
    margin: 0;
    font-size: 12px;
    color: #cccccc;
    line-height: 1.4;
  }

  .example-code {
    position: relative;
  }

  .copy-code-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #e91e63;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    cursor: pointer;
    z-index: 1;
    transition: background-color 0.2s;
  }

  .copy-code-btn:hover {
    background: #c2185b;
  }

  .example-code pre {
    margin: 0;
    padding: 12px;
    background: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .example-code code {
    background: none;
    padding: 0;
    color: inherit;
  }

  .api-item.data {
    border-left-color: #2196f3;
  }

  .api-item.scene {
    border-left-color: #e91e63;
  }

  .item-name {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 2px;
  }

  .item-desc {
    display: block;
    font-size: 10px;
    color: #aaaaaa;
    line-height: 1.3;
  }

  /* 滚动条样式 */
  .content-area::-webkit-scrollbar {
    width: 6px;
  }

  .content-area::-webkit-scrollbar-track {
    background: #2d2d2d;
  }

  .content-area::-webkit-scrollbar-thumb {
    background: #555555;
    border-radius: 3px;
  }

  .content-area::-webkit-scrollbar-thumb:hover {
    background: #666666;
  }

  /* 对象选择器样式 */
  .object-selector {
    margin-bottom: 12px;
    padding: 8px;
    border-bottom: 1px solid #404040;
    /* 限制选择器容器 */
    position: relative;
    overflow: visible; /* 允许下拉列表显示，但会被父容器限制 */
  }

  .object-select {
    width: 100%;
    padding: 6px 8px;
    background: #404040;
    border: 1px solid #555555;
    border-radius: 3px;
    color: #ffffff;
    font-size: 11px;
    cursor: pointer;
    /* 强制限制select的显示宽度 */
    max-width: 100%;
    box-sizing: border-box;
  }

  /* 当select获得焦点时，限制下拉列表的显示 */
  .object-select:focus {
    outline: none;
    border-color: #007acc;
    /* 尝试用transform来缩放下拉选项 */
    transform-origin: top left;
  }



  .object-select option {
    background: #404040;
    color: #ffffff;
  }

  .object-select optgroup {
    background: #333333;
    color: #cccccc;
    font-weight: bold;
  }

  /* 自定义下拉框样式 */
  .custom-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 6px 8px;
    background: #404040;
    border: 1px solid #555555;
    border-radius: 3px;
    color: #ffffff;
    font-size: 11px;
    cursor: pointer;
    user-select: none;
  }

  .custom-select:hover {
    border-color: #007acc;
  }

  .select-display {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .select-arrow {
    margin-left: 8px;
    font-size: 10px;
    transition: transform 0.2s;
  }

  .dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #404040;
    border: 1px solid #555555;
    border-top: none;
    border-radius: 0 0 3px 3px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .dropdown-group {
    border-bottom: 1px solid #555555;
  }

  .dropdown-group:last-child {
    border-bottom: none;
  }

  .group-title {
    padding: 6px 8px;
    background: #333333;
    color: #cccccc;
    font-weight: bold;
    font-size: 10px;
    border-bottom: 1px solid #555555;
  }

  .dropdown-item {
    padding: 6px 8px;
    color: #ffffff;
    font-size: 11px;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: background-color 0.2s;
  }

  .dropdown-item:hover {
    background: #4a4a4a;
  }

  .dropdown-item:active {
    background: #007acc;
  }
</style>
