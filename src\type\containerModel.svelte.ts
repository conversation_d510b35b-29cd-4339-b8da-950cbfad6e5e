import { BaseObjectModel } from './baseObjectModel.svelte';

/**
 * 响应式容器模型类
 * 管理容器对象及其子对象的响应式状态
 */
export class ContainerModel extends BaseObjectModel {

    constructor(container: any) {
        super(container);

        // setupSync() 已经在基类构造函数中调用了
    }


    /**
     * 设置Container特有属性同步（重写基类方法）
     * Container通常没有特有属性需要同步
     */
    protected setupSpecificSync(): void {
        // Container通常没有特有的属性需要同步
        // 如果将来有Container特有的属性，在这里添加
        console.log('🔧 ContainerModel: Container特有属性已同步');
    }

    /**
     * 递归查找子对象（包括子容器中的对象）
     */
    findChildRecursive(predicate: (child: BaseObjectModel) => boolean): BaseObjectModel | null {
        // 首先在直接子对象中查找
        const directChild = this.findChild(predicate);
        if (directChild) return directChild;

        // 然后在子容器中递归查找
        for (const child of this.children) {
            if (child instanceof ContainerModel) {
                const found = child.findChildRecursive(predicate);
                if (found) return found;
            }
        }

        return null;
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        return `${indent}const ${varName} = new PIXI.Container();`;
    }

    /**
     * 克隆Container对象（实现抽象方法）
     * @returns 克隆的ContainerModel实例
     */
    clone(): ContainerModel {
        console.log('🔄 ContainerModel: 开始克隆Container对象（手动创建）');

        // 1. 获取原始Container对象
        const originalContainer = this.getOriginalObject();
        if (!originalContainer) {
            console.error('❌ ContainerModel: 原始对象不存在');
            throw new Error('Container 原始对象不存在');
        }

        // 2. 🔑 手动创建新的Container对象（因为PIXI.Container没有clone方法）
        const clonedContainer = new PIXI.Container();

        // 复制基础属性（添加安全检查）
        try {
            clonedContainer.x = (typeof originalContainer.x === 'number' ? originalContainer.x : 0) + 20;
            clonedContainer.y = (typeof originalContainer.y === 'number' ? originalContainer.y : 0) + 20;

            if (typeof originalContainer.visible === 'boolean') {
                clonedContainer.visible = originalContainer.visible;
            }
            if (typeof originalContainer.alpha === 'number') {
                clonedContainer.alpha = originalContainer.alpha;
            }
            if (typeof originalContainer.rotation === 'number') {
                clonedContainer.rotation = originalContainer.rotation;
            }

            // 安全设置 scale 属性
            if (originalContainer.scale && typeof originalContainer.scale === 'object' &&
                clonedContainer.scale && typeof clonedContainer.scale === 'object') {
                if (typeof originalContainer.scale.x === 'number') {
                    clonedContainer.scale.x = originalContainer.scale.x;
                }
                if (typeof originalContainer.scale.y === 'number') {
                    clonedContainer.scale.y = originalContainer.scale.y;
                }
            }
        } catch (error) {
            console.error('❌ ContainerModel: 复制基础属性时出错', error);
            // 设置默认值
            clonedContainer.x = 20;
            clonedContainer.y = 20;
        }

        // 3. 先克隆所有子对象并添加到 clonedContainer 中
        console.log(`🔄 ContainerModel: 开始克隆 ${this.children.length} 个子对象`);

        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            try {
                if (typeof childModel.clone === 'function') {
                    console.log(`🔄 ContainerModel: 克隆子对象 [${i}]: ${childModel.className}`);
                    const clonedChildModel = childModel.clone();

                    // 🔑 将克隆的子对象的原始对象添加到克隆的Container中
                    const clonedChildOriginal = clonedChildModel.getOriginalObject();
                    if (clonedChildOriginal) {
                        clonedContainer.addChild(clonedChildOriginal);
                        console.log(`✅ ContainerModel: 子对象 [${i}] 已添加到容器 (${childModel.className})`);
                    } else {
                        console.warn(`⚠️ ContainerModel: 子对象 [${i}] 的原始对象为空`);
                    }
                } else {
                    console.warn(`⚠️ ContainerModel: 子对象 [${i}] 没有 clone 方法: ${childModel.className}`);
                }
            } catch (error) {
                console.error(`❌ ContainerModel: 克隆子对象 [${i}] 失败:`, error);
                // 继续处理其他子对象，不中断整个克隆过程
            }
        }

        // 4. 🔑 现在创建 ContainerModel，它会自动检测并创建子对象的模型
        console.log(`🔄 ContainerModel: 创建新的 ContainerModel，包含 ${clonedContainer.children.length} 个子对象`);
        const clonedModel = new ContainerModel(clonedContainer);

        console.log('✅ ContainerModel: 克隆完成，包含', clonedModel.children.length, '个子对象');
        return clonedModel;
    }
}

// 注册ContainerModel到基类容器
BaseObjectModel.registerModel('Container', ContainerModel);