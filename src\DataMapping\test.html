<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataMapping 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #2980b9;
        }
        .info {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 6px;
            margin: 16px 0;
            border-left: 4px solid #3498db;
        }
        .code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 DataMapping 系统测试</h1>

        <div class="info">
            <h3>测试说明</h3>
            <p>这个页面用于测试新的DataMapping系统。点击下面的按钮来测试不同的功能。</p>
        </div>

        <h3>🧪 功能测试</h3>
        <button class="test-btn" onclick="testGetSceneData()">获取Scene_Title数据</button>
        <button class="test-btn" onclick="testGetCurrentScene()">获取当前场景</button>
        <button class="test-btn" onclick="testModalService()">测试模态框服务</button>

        <div id="output"></div>

        <div class="info">
            <h3>📋 支持的场景</h3>
            <div class="code">
                <div><strong>Scene_Title:</strong> 3个按钮 (NewGame, Continue, Options)</div>
                <div><strong>Scene_Map:</strong> 4个按钮 (Menu, Save, Load, Transfer)</div>
                <div><strong>Scene_Menu:</strong> 8个按钮 (Item, Skill, Equip, Status, Formation, Options, Save, Game End)</div>
            </div>
        </div>

        <div class="info">
            <h3>🎨 优化后的设计特点</h3>
            <ul>
                <li>✨ 使用全局主题样式</li>
                <li>🎯 紧凑的卡片布局</li>
                <li>🎮 左侧导航单场景显示</li>
                <li>🖱️ 可拖拽移动窗口</li>
                <li>📊 动态统计信息</li>
                <li>🎨 统一的色彩系统</li>
                <li>🔄 响应式网格布局</li>
                <li>💫 流畅的悬停动画</li>
                <li>🌙 支持主题切换</li>
                <li>📱 紧凑的间距设计</li>
            </ul>
        </div>
    </div>

    <script type="module">
        // 模拟RPG Maker MZ环境
        window.SceneManager = {
            _scene: {
                constructor: { name: 'Scene_Title' }
            }
        };

        // 导入DataMapping模块
        import { getSceneDataFlow, getCurrentSceneName } from './index.js';

        // 测试函数
        window.testGetSceneData = function() {
            const data = getSceneDataFlow('Scene_Title');
            displayOutput('Scene_Title数据:', data);
        };

        window.testGetCurrentScene = function() {
            const sceneName = getCurrentSceneName();
            displayOutput('当前场景:', sceneName);
        };

        window.testModalService = function() {
            displayOutput('模态框服务测试:', '请在主应用中测试模态框功能');
        };

        function displayOutput(title, data) {
            const output = document.getElementById('output');
            const div = document.createElement('div');
            div.className = 'info';
            div.innerHTML = `
                <h4>${title}</h4>
                <pre class="code">${JSON.stringify(data, null, 2)}</pre>
            `;
            output.appendChild(div);
        }

        console.log('DataMapping 测试页面已加载');
    </script>
</body>
</html>
