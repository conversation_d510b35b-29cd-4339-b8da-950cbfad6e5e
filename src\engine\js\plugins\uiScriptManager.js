/*:
 * @target MZ
 * @plugindesc UIScriptManager v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description 统一的脚本管理器，为任何对象添加脚本功能
 *
 * @help uiScriptManager.js
 *
 * UIScriptManager 提供以下功能：
 * 1. 为任何对象添加脚本数组
 * 2. 统一的脚本执行引擎
 * 3. 默认脚本模板（只有start启用，其他注释）
 * 4. 脚本管理方法（添加、删除、启用、禁用）
 */

(() => {
    'use strict';

    /**
     * UIScriptManager - 统一的脚本管理器
     * 专门处理对象的脚本功能，与UIComponent分离
     */
    window.UIScriptManager = {
        
        /**
         * 为对象添加脚本功能
         * @param {Object} obj 目标对象
         * @param {Object} properties 属性配置
         */
        applyToObject(obj, properties = {}) {
            console.log(`🔧 UIScriptManager: 为${obj.constructor.name}添加脚本功能`);
            
            // 🔑 添加脚本数组
            obj.componentScripts = properties.componentScripts || this.createDefaultScripts();
            
            // 🔑 添加脚本管理方法
            this.addScriptMethods(obj);
            
            console.log(`✅ UIScriptManager: ${obj.constructor.name}脚本功能已添加`, obj.componentScripts);
            
            return obj;
        },

        /**
         * 创建默认脚本数组
         * 只有一个默认脚本，包含所有方法模板
         */
        createDefaultScripts() {
            return [
                {
                    id: 'default_script',
                    name: '默认脚本',
                    enabled: true,
                    code: `/**
 * 🚀 onStart - 对象启动生命周期
 * 触发时机: 对象创建并添加到父容器时自动触发
 * 作用: 初始化对象状态、设置默认值、绑定数据等
 * 配合方法: 无需手动调用，系统自动触发
 */
function onStart() {
  console.log("对象启动:", self.name || "unnamed");
  // 在这里添加初始化逻辑
  // 例如: 设置初始文本、绑定数据源、初始化变量等
}

/**
 * 🔄 onUpdate - 每帧更新生命周期
 * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用
 * 作用: 实现动画、实时数据更新、状态检查等
 * 配合方法: 无需手动调用，系统自动触发
 * 注意: 避免在此方法中执行耗时操作
 */
// function onUpdate() {
//   console.log("每帧更新:", self.name);
//   // 在这里添加每帧更新逻辑
//   // 例如: 动画更新、实时数据显示、状态检查等
// }

/**
 * 📝 onFieldUpdate - 字段更新生命周期
 * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发
 * 作用: 响应数据变化、更新UI显示、同步状态等
 * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)
 * 参数: ctx - 包含字段更新上下文信息的对象
 */
// function onFieldUpdate(ctx) {
//   console.log("字段更新:", self.name, ctx);
//   // 在这里添加字段更新时的处理逻辑
//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }
//   // 例如: 根据 ctx.field 判断更新哪个属性
// }

/**
 * 🗑️ onDestroy - 对象销毁生命周期
 * 触发时机: 对象从父容器移除时自动触发
 * 作用: 清理资源、保存数据、解除绑定等
 * 配合方法: 无需手动调用，系统自动触发
 */
// function onDestroy() {
//   console.log("对象销毁:", self.name);
//   // 在这里添加清理逻辑
//   // 例如: 清理定时器、保存状态、解除事件绑定等
// }

/**
 * 👆 onClick - 单击交互事件
 * 触发时机: 用户单击对象时触发
 * 作用: 处理点击逻辑、切换状态、执行操作等
 * 配合方法: 无需手动调用，用户交互自动触发
 */
// function onClick() {
//   console.log("对象被点击:", self.name);
//   // 在这里添加点击逻辑
//   // 例如: 切换状态、打开菜单、执行命令等
// }

/**
 * 👆👆 onDoubleClick - 双击交互事件
 * 触发时机: 用户双击对象时触发
 * 作用: 处理双击特殊逻辑、快捷操作等
 * 配合方法: 无需手动调用，用户交互自动触发
 */
// function onDoubleClick() {
//   console.log("对象被双击:", self.name);
//   // 在这里添加双击逻辑
//   // 例如: 快速编辑、全屏显示、快捷操作等
// }

/**
 * 🖱️ onHover - 鼠标悬停事件
 * 触发时机: 鼠标进入对象区域时触发
 * 作用: 显示提示信息、改变外观、预览效果等
 * 配合方法: 通常与 onHoverOut 配对使用
 */
// function onHover() {
//   console.log("鼠标悬停:", self.name);
//   // 在这里添加悬停逻辑
//   // 例如: 显示工具提示、改变颜色、显示预览等
// }

/**
 * 🖱️ onHoverOut - 鼠标离开事件
 * 触发时机: 鼠标离开对象区域时触发
 * 作用: 隐藏提示信息、恢复外观、清理预览等
 * 配合方法: 通常与 onHover 配对使用
 */
// function onHoverOut() {
//   console.log("鼠标离开:", self.name);
//   // 在这里添加鼠标离开逻辑
//   // 例如: 隐藏工具提示、恢复颜色、清理预览等
// }

/**
 * ⬇️ onPress - 按下事件
 * 触发时机: 鼠标按下（但未释放）时触发
 * 作用: 开始拖拽、显示按下效果、记录按下状态等
 * 配合方法: 通常与 onRelease 配对使用
 */
// function onPress() {
//   console.log("对象按下:", self.name);
//   // 在这里添加按下逻辑
//   // 例如: 开始拖拽、改变外观、记录状态等
// }

/**
 * ⬆️ onRelease - 释放事件
 * 触发时机: 鼠标释放时触发
 * 作用: 结束拖拽、恢复外观、完成操作等
 * 配合方法: 通常与 onPress 配对使用
 */
// function onRelease() {
//   console.log("对象释放:", self.name);
//   // 在这里添加释放逻辑
//   // 例如: 结束拖拽、恢复外观、完成操作等
// }`,
                    description: '默认脚本，包含常用方法模板'
                }
            ];
        },

        /**
         * 脚本模板定义
         */
        SCRIPT_TEMPLATES: {
            // 生命周期脚本
            start: {
                name: 'start',
                type: 'lifecycle',
                icon: '🚀',
                description: '对象创建时执行',
                defaultCode: 'console.log("对象启动:", self.name || "unnamed");'
            },
            
            update: {
                name: 'update',
                type: 'lifecycle',
                icon: '🔄',
                description: '每帧更新时执行',
                defaultCode: 'console.log("每帧更新:", self.name || "unnamed");'
            },
            
            destroy: {
                name: 'destroy', 
                type: 'lifecycle',
                icon: '💥',
                description: '对象销毁时执行',
                defaultCode: 'console.log("对象销毁:", self.name || "unnamed");'
            },
            
            // 交互事件脚本
            onClick: {
                name: 'onClick',
                type: 'interaction',
                icon: '🖱️',
                description: '点击时执行',
                defaultCode: 'console.log("对象被点击:", self.name || "unnamed");'
            },
            
            onHover: {
                name: 'onHover',
                type: 'interaction', 
                icon: '🎯',
                description: '鼠标悬停时执行',
                defaultCode: 'console.log("鼠标悬停:", self.name || "unnamed");'
            },

            onHoverOut: {
                name: 'onHoverOut',
                type: 'interaction',
                icon: '🚪', 
                description: '鼠标离开时执行',
                defaultCode: 'console.log("鼠标离开:", self.name || "unnamed");'
            }
        },

        /**
         * 为对象添加脚本管理方法
         * @param {Object} obj 目标对象
         */
        addScriptMethods(obj) {
            // 🔑 脚本缓存 - 每个脚本只编译一次
            obj._scriptCache = obj._scriptCache || new Map();

            // 🔑 执行指定方法名的脚本
            obj.executeScript = function(methodName, ...args) {
                if (!this.componentScripts) {
                    // console.log(`⚠️ 对象 ${this.constructor.name} 没有脚本数组`);
                    return;
                }

                // console.log(`⚡ 尝试执行方法: ${methodName}，脚本数量: ${this.componentScripts.length}`);

                // 遍历所有启用的脚本
                this.componentScripts.forEach((script, index) => {
                    // console.log(`🔍 检查脚本 ${index}: "${script.name}", 启用: ${script.enabled}, 有代码: ${!!script.code}`);

                    if (!script.enabled || !script.code || !script.code.trim()) {
                        // console.log(`⏭️ 跳过脚本 "${script.name}": 未启用或无代码`);
                        return;
                    }

                    try {
                        // console.log(`🔧 执行脚本 "${script.name}" 中的方法 "${methodName}"`);

                        // 🔑 检查缓存，如果脚本代码没有变化，使用缓存的方法
                        const cacheKey = `${script.id}_${script.name}`;
                        let scriptContext = this._scriptCache.get(cacheKey);

                        // 如果缓存不存在或脚本代码已更改，重新编译
                        if (!scriptContext || scriptContext.code !== script.code) {
                            console.log(`🔧 编译脚本: "${script.name}"`);

                            // 创建脚本执行上下文
                            const scriptScope = {
                                self: this,
                                console: console,
                                $gameParty: window.$gameParty,
                                $gameActors: window.$gameActors,
                                $gameVariables: window.$gameVariables,
                                $gameSwitches: window.$gameSwitches,
                                AudioManager: window.AudioManager,
                                SceneManager: window.SceneManager
                            };

                            // 执行脚本代码，在 scriptScope 中定义变量和函数
                            const func = new Function('scriptScope', `
                                with (scriptScope) {
                                    ${script.code}

                                    // 返回所有定义的函数
                                    return {
                                        // 🔑 生命周期方法
                                        onStart: typeof onStart !== 'undefined' ? onStart : null,
                                        onUpdate: typeof onUpdate !== 'undefined' ? onUpdate : null,
                                        onFieldUpdate: typeof onFieldUpdate !== 'undefined' ? onFieldUpdate : null,
                                        onDestroy: typeof onDestroy !== 'undefined' ? onDestroy : null,

                                        // 🔑 交互事件方法
                                        onClick: typeof onClick !== 'undefined' ? onClick : null,
                                        onHover: typeof onHover !== 'undefined' ? onHover : null,
                                        onHoverOut: typeof onHoverOut !== 'undefined' ? onHoverOut : null,
                                        onPress: typeof onPress !== 'undefined' ? onPress : null,
                                        onRelease: typeof onRelease !== 'undefined' ? onRelease : null,
                                        onDoubleClick: typeof onDoubleClick !== 'undefined' ? onDoubleClick : null,

                                        // 🔑 数据事件方法（UISlider 和 UISwitch 专用）
                                        onChange: typeof onChange !== 'undefined' ? onChange : null,
                                        onSliderStart: typeof onSliderStart !== 'undefined' ? onSliderStart : null,
                                        onSliderEnd: typeof onSliderEnd !== 'undefined' ? onSliderEnd : null
                                    };
                                }
                            `);

                            const methods = func(scriptScope);

                            // 缓存编译结果和作用域
                            scriptContext = {
                                code: script.code,
                                methods: methods,
                                scope: scriptScope
                            };
                            this._scriptCache.set(cacheKey, scriptContext);
                        }

                        // 检查是否存在指定方法并执行
                        if (scriptContext.methods && typeof scriptContext.methods[methodName] === 'function') {
                            // console.log(`✅ 找到并执行方法 "${methodName}" 在脚本 "${script.name}"`);

                            // 更新作用域中的参数（保持向后兼容）
                            scriptContext.scope.data = args[0];

                            // 在保持变量状态的作用域中执行方法，传递所有参数
                            scriptContext.methods[methodName].call(this, ...args);
                        } else {
                            // console.log(`⚠️ 脚本 "${script.name}" 中未找到方法 "${methodName}"`);
                        }

                    } catch (error) {
                        console.error(`❌ 脚本执行失败 ${script.name}.${methodName}:`, error);
                    }
                });
            };

            // 🔑 添加脚本
            obj.addScript = function(scriptTemplate) {
                const newScript = {
                    id: `script_${Date.now()}`,
                    name: scriptTemplate.name,
                    type: scriptTemplate.type,
                    enabled: true,
                    code: scriptTemplate.defaultCode || '',
                    description: scriptTemplate.description || ''
                };
                
                if (!this.componentScripts) {
                    this.componentScripts = [];
                }
                
                this.componentScripts.push(newScript);
                return newScript;
            };

            // 🔑 删除脚本
            obj.removeScript = function(scriptId) {
                if (this.componentScripts) {
                    this.componentScripts = this.componentScripts.filter(s => s.id !== scriptId);
                }
            };

            // 🔑 更新脚本
            obj.updateScript = function(scriptId, updates) {
                const script = this.componentScripts?.find(s => s.id === scriptId);
                if (script) {
                    Object.assign(script, updates);

                    // 🔑 清理缓存，强制重新编译
                    if (this._scriptCache) {
                        const cacheKey = `${script.id}_${script.name}`;
                        this._scriptCache.delete(cacheKey);
                        console.log(`🔧 已清理脚本缓存: ${script.name}`);
                    }

                    console.log(`✅ 脚本已更新: ${scriptId}`, updates);
                }
            };

            // 🔑 启用/禁用脚本
            obj.toggleScript = function(scriptId, enabled) {
                const script = this.componentScripts?.find(s => s.id === scriptId);
                if (script) {
                    script.enabled = enabled;
                }
            };

            // 🔑 获取脚本
            obj.getScript = function(scriptId) {
                return this.componentScripts?.find(s => s.id === scriptId);
            };

            // 🔑 按名称获取脚本
            obj.getScriptByName = function(name) {
                return this.componentScripts?.find(s => s.name === name);
            };

            // 🔑 获取所有启用的脚本
            obj.getEnabledScripts = function() {
                return this.componentScripts?.filter(s => s.enabled) || [];
            };

            // 🔑 获取指定类型的脚本
            obj.getScriptsByType = function(type) {
                return this.componentScripts?.filter(s => s.type === type) || [];
            };
        }
    };

    console.log('✅ UIScriptManager: 脚本管理器已加载');

})();
