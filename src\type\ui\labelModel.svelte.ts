import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * 脚本接口定义
 */
interface Script {
    id: string;
    name: string;
    type: 'lifecycle' | 'interaction' | 'custom';
    enabled: boolean;
    code: string;
    description: string;
}

export class LabelModel extends BaseObjectModel {

    constructor(label: any) {
        super(label);

        // 初始化文本特有属性
        this.width = label.width || 200;
        this.height = label.height || 40;
        this.text = label.text || 'Label Text';
        this.prefix = label.prefix || '';
        this.suffix = label.suffix || '';



        // 🔑 统一的脚本系统 - 从原始对象获取脚本数组
        this.componentScripts = label.componentScripts || [];

        // 🔑 如果没有脚本，添加默认脚本
        if (this.componentScripts.length === 0) {
            this.componentScripts = [{
                id: 'default_lifecycle',
                name: '生命周期脚本',
                type: 'lifecycle',
                enabled: true,
                description: '默认的生命周期脚本',
                code: `/**
 * 🚀 onStart - 对象启动生命周期
 * 触发时机: 对象创建并添加到父容器时自动触发
 * 作用: 初始化对象状态、设置默认值、绑定数据等
 * 配合方法: 无需手动调用，系统自动触发
 */
function onStart() {
  console.log("对象启动:", self.name || "unnamed");
  // 在这里添加初始化逻辑
  // 例如: 设置初始文本、绑定数据源、初始化变量等
}

/**
 * 🔄 onUpdate - 每帧更新生命周期
 * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用
 * 作用: 实现动画、实时数据更新、状态检查等
 * 配合方法: 无需手动调用，系统自动触发
 * 注意: 避免在此方法中执行耗时操作
 */
// function onUpdate() {
//   console.log("每帧更新:", self.name);
//   // 在这里添加每帧更新逻辑
//   // 例如: 动画更新、实时数据显示、状态检查等
// }

/**
 * 📝 onFieldUpdate - 字段更新生命周期
 * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发
 * 作用: 响应数据变化、更新UI显示、同步状态等
 * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)
 * 参数: ctx - 包含字段更新上下文信息的对象
 */
// function onFieldUpdate(ctx) {
//   console.log("字段更新:", self.name, ctx);
//   // 在这里添加字段更新时的处理逻辑
//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }
//   // 例如: 根据 ctx.field 判断更新哪个属性
// }

/**
 * 🗑️ onDestroy - 对象销毁生命周期
 * 触发时机: 对象从父容器移除时自动触发
 * 作用: 清理资源、保存数据、解除绑定等
 * 配合方法: 无需手动调用，系统自动触发
 */
// function onDestroy() {
//   console.log("对象销毁:", self.name);
//   // 在这里添加清理逻辑
//   // 例如: 清理定时器、保存状态、解除事件绑定等
// }`
            }];
        }

        console.log('🔧 LabelModel: 脚本系统初始化', {
            hasOriginalScripts: !!label.componentScripts,
            scriptsCount: this.componentScripts.length,
            scriptNames: this.componentScripts.map(s => s.name)
        });
        this.fontSize = label.fontSize || 16;
        this.fontFace = label.fontFace || 'GameFont';
        this.fontBold = label.fontBold || false;
        this.fontItalic = label.fontItalic || false;
        this.textColor = label.textColor || '#ffffff';
        this.outlineColor = label.outlineColor || '#000000';
        this.outlineWidth = label.outlineWidth || 4;
        this.textAlign = label.textAlign || 'center';
        this.verticalAlign = label.verticalAlign || 'middle';
        this.backgroundColor = label.backgroundColor || 'transparent';
        this.backgroundOpacity = label.backgroundOpacity || 1;

        // 初始化间距属性
        this.letterSpacing = label.letterSpacing || 0;
        this.wordSpacing = label.wordSpacing || 0;
        this.lineHeight = label.lineHeight || 1.2;
        this.spacingMode = label.spacingMode || 'auto';

        console.log('🔧 LabelModel: 创建文本模型', label);

        // setupSync() 已经在基类构造函数中调用了
    }



    // 文本内容属性
    text = $state('Label Text');        // 文本内容
    prefix = $state('');                // 前缀
    suffix = $state('');                // 后缀



    // 🔑 统一的脚本系统
    componentScripts = $state<Script[]>([]);   // 脚本数组

    // 字体属性
    fontSize = $state(16);              // 字体大小
    fontFace = $state('GameFont');      // 字体名称
    fontBold = $state(false);           // 是否粗体
    fontItalic = $state(false);         // 是否斜体

    // 颜色属性
    textColor = $state('#ffffff');      // 文本颜色
    outlineColor = $state('#000000'); // 描边颜色
    outlineWidth = $state(4);           // 描边宽度

    // 对齐属性
    textAlign = $state('center');       // 水平对齐: left, center, right
    verticalAlign = $state('middle');   // 垂直对齐: top, middle, bottom

    // 🔑 间距属性
    letterSpacing = $state(0);          // 字符间距
    wordSpacing = $state(0);            // 单词间距
    lineHeight = $state(1.2);           // 行高
    spacingMode = $state('auto');       // 渲染模式: auto, bitmap, canvas

    // 背景属性
    backgroundColor = $state('transparent'); // 背景颜色
    backgroundOpacity = $state(1);      // 背景透明度

    /**
     * 设置Label特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Label特有的属性
     */
    protected setupSpecificSync(): void {
        console.log('🔧 LabelModel: setupSpecificSync 被调用');

        // 同步文本特有属性
        this._originalObject.labelWidth = this.width;
        this._originalObject.labelHeight = this.height;

        // 🔑 同步组件脚本数组
        if (this._originalObject.componentScripts !== this.componentScripts) {
            this._originalObject.componentScripts = this.componentScripts;
            console.log('🔧 LabelModel: 同步组件脚本', this.componentScripts);
        }

        // 同步文本内容
        if (this._originalObject.text !== this.text) {
            this._originalObject.text = this.text;
            if (this._originalObject.setText && typeof this._originalObject.setText === 'function') {
                this._originalObject.setText(this.text);
            }
        }

        // 同步前缀和后缀
        if (this._originalObject.prefix !== this.prefix) {
            this._originalObject.prefix = this.prefix;
            if (this._originalObject.setPrefix && typeof this._originalObject.setPrefix === 'function') {
                this._originalObject.setPrefix(this.prefix);
            }
        }

        if (this._originalObject.suffix !== this.suffix) {
            this._originalObject.suffix = this.suffix;
            if (this._originalObject.setSuffix && typeof this._originalObject.setSuffix === 'function') {
                this._originalObject.setSuffix(this.suffix);
            }
        }

        // 同步字体属性
        if (this._originalObject.fontSize !== this.fontSize) {
            this._originalObject.fontSize = this.fontSize;
            if (this._originalObject.setFontSize && typeof this._originalObject.setFontSize === 'function') {
                this._originalObject.setFontSize(this.fontSize);
            }
        }

        this._originalObject.fontFace = this.fontFace;
        this._originalObject.fontBold = this.fontBold;
        this._originalObject.fontItalic = this.fontItalic;

        // 同步颜色属性
        if (this._originalObject.textColor !== this.textColor) {
            this._originalObject.textColor = this.textColor;
            if (this._originalObject.setTextColor && typeof this._originalObject.setTextColor === 'function') {
                this._originalObject.setTextColor(this.textColor);
            }
        }

        if (this._originalObject.outlineColor !== this.outlineColor) {
            this._originalObject.outlineColor = this.outlineColor;
            if (this._originalObject.setOutlineColor && typeof this._originalObject.setOutlineColor === 'function') {
                this._originalObject.setOutlineColor(this.outlineColor);
            }
        }

        if (this._originalObject.outlineWidth !== this.outlineWidth) {
            this._originalObject.outlineWidth = this.outlineWidth;
            if (this._originalObject.bitmap) {
                this._originalObject.bitmap.outlineWidth = this.outlineWidth;
            }
            if (this._originalObject.setOutlineWidth && typeof this._originalObject.setOutlineWidth === 'function') {
                this._originalObject.setOutlineWidth(this.outlineWidth);
            }
        }

        // 同步对齐属性
        if (this._originalObject.textAlign !== this.textAlign) {
            this._originalObject.textAlign = this.textAlign;
            if (this._originalObject.setTextAlign && typeof this._originalObject.setTextAlign === 'function') {
                this._originalObject.setTextAlign(this.textAlign);
            }
        }

        if (this._originalObject.verticalAlign !== this.verticalAlign) {
            this._originalObject.verticalAlign = this.verticalAlign;
            if (this._originalObject.setVerticalAlign && typeof this._originalObject.setVerticalAlign === 'function') {
                this._originalObject.setVerticalAlign(this.verticalAlign);
            }
        }

        // 同步背景属性
        if (this._originalObject.backgroundColor !== this.backgroundColor) {
            this._originalObject.backgroundColor = this.backgroundColor;
            if (this._originalObject.setBackgroundColor && typeof this._originalObject.setBackgroundColor === 'function') {
                this._originalObject.setBackgroundColor(this.backgroundColor);
            }
        }

        this._originalObject.backgroundOpacity = this.backgroundOpacity;

        // 🔑 同步间距属性
        if (this._originalObject.letterSpacing !== this.letterSpacing) {
            this._originalObject.letterSpacing = this.letterSpacing;
            if (this._originalObject.setLetterSpacing && typeof this._originalObject.setLetterSpacing === 'function') {
                this._originalObject.setLetterSpacing(this.letterSpacing);
            }
        }

        if (this._originalObject.wordSpacing !== this.wordSpacing) {
            this._originalObject.wordSpacing = this.wordSpacing;
            if (this._originalObject.setWordSpacing && typeof this._originalObject.setWordSpacing === 'function') {
                this._originalObject.setWordSpacing(this.wordSpacing);
            }
        }

        if (this._originalObject.lineHeight !== this.lineHeight) {
            this._originalObject.lineHeight = this.lineHeight;
            if (this._originalObject.setLineHeight && typeof this._originalObject.setLineHeight === 'function') {
                this._originalObject.setLineHeight(this.lineHeight);
            }
        }

        this._originalObject.spacingMode = this.spacingMode;

        // 同步尺寸（如果有变化则重新设置）
        if (this._originalObject.setSize && typeof this._originalObject.setSize === 'function') {
            this._originalObject.setSize(this.width, this.height);
        }
    }

    /**
     * 设置文本内容
     */
    public setText(text: string): void {
        this.text = text;
    }

    /**
     * 设置字体大小
     */
    public setFontSize(size: number): void {
        this.fontSize = size;
    }

    /**
     * 设置文本颜色
     */
    public setTextColor(color: string): void {
        this.textColor = color;
    }

    /**
     * 设置描边
     */
    public setOutline(color: string, width: number): void {
        this.outlineColor = color;
        this.outlineWidth = width;
    }

    /**
     * 设置对齐方式
     */
    public setAlignment(horizontal: string, vertical: string): void {
        this.textAlign = horizontal;
        this.verticalAlign = vertical;
    }

    /**
     * 设置文本区域尺寸
     */
    public setLabelSize(width: number, height: number): void {
        this.width = width;
        this.height = height;
    }

    /**
     * 设置背景
     */
    public setBackground(color: string, opacity: number = 1): void {
        this.backgroundColor = color;
        this.backgroundOpacity = opacity;
    }

    /**
     * 设置字符间距
     */
    public setLetterSpacing(spacing: number): void {
        this.letterSpacing = spacing;
    }

    /**
     * 设置单词间距
     */
    public setWordSpacing(spacing: number): void {
        this.wordSpacing = spacing;
    }

    /**
     * 设置行高
     */
    public setLineHeight(height: number): void {
        this.lineHeight = height;
    }

    /**
     * 批量设置间距
     */
    public setSpacing(options: { letter?: number; word?: number; line?: number }): void {
        if (options.letter !== undefined) {
            this.letterSpacing = options.letter;
        }
        if (options.word !== undefined) {
            this.wordSpacing = options.word;
        }
        if (options.line !== undefined) {
            this.lineHeight = options.line;
        }
    }

    /**
     * 获取文本信息
     */
    public getTextInfo(): {
        text: string;
        prefix: string;
        suffix: string;
        font: { face: string; size: number; bold: boolean; italic: boolean };
        colors: { text: string; outline: string; background: string };
        alignment: { horizontal: string; vertical: string };
        size: { width: number; height: number };
    } {
        return {
            text: this.text,
            prefix: this.prefix,
            suffix: this.suffix,
            font: {
                face: this.fontFace,
                size: this.fontSize,
                bold: this.fontBold,
                italic: this.fontItalic
            },
            colors: {
                text: this.textColor,
                outline: this.outlineColor,
                background: this.backgroundColor
            },
            alignment: {
                horizontal: this.textAlign,
                vertical: this.verticalAlign
            },
            size: {
                width: this.width,
                height: this.height
            }
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 创建 UILabel 对象，分离静态文本和数据绑定
        codes.push(`${indent}const ${varName} = new UILabel({`);

        // 生成静态文本
        codes.push(`${indent}    text: '${this.text.replace(/'/g, "\\'")}',`);

        // 🔑 生成组件脚本数组（如果有的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            codes.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, `\n${indent}`)},`);
        }

        codes.push(`${indent}    prefix: '${this.prefix.replace(/'/g, "\\'")}',`);
        codes.push(`${indent}    suffix: '${this.suffix.replace(/'/g, "\\'")}',`);
        codes.push(`${indent}    fontSize: ${this.fontSize},`);
        codes.push(`${indent}    fontFace: '${this.fontFace}',`);
        codes.push(`${indent}    fontBold: ${this.fontBold},`);
        codes.push(`${indent}    fontItalic: ${this.fontItalic},`);
        codes.push(`${indent}    textColor: '${this.textColor}',`);
        codes.push(`${indent}    outlineColor: '${this.outlineColor}',`);
        codes.push(`${indent}    outlineWidth: ${this.outlineWidth},`);
        codes.push(`${indent}    textAlign: '${this.textAlign}',`);
        codes.push(`${indent}    verticalAlign: '${this.verticalAlign}',`);

        if (this.backgroundColor !== 'transparent') {
            codes.push(`${indent}    backgroundColor: '${this.backgroundColor}',`);
            codes.push(`${indent}    backgroundOpacity: ${this.backgroundOpacity},`);
        }

        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 🔑 检查是否有非空的脚本内容
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

    /**
     * 克隆当前标签对象 - 调用插件的 clone 方法
     */
    clone(): LabelModel {
        console.log('🔄 LabelModel: 开始克隆标签对象（调用插件方法）');

        // 1. 调用原始 UILabel 对象的 clone 方法
        const originalUILabel = this.getOriginalObject();
        if (!originalUILabel || typeof originalUILabel.clone !== 'function') {
            console.error('❌ LabelModel: 原始对象没有 clone 方法');
            throw new Error('UILabel 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUILabel = originalUILabel.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 LabelModel 包装克隆的对象
        const clonedModel = new LabelModel(clonedUILabel);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ LabelModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UILabel 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UILabel 特有属性
            text: this.text,
            prefix: this.prefix,
            suffix: this.suffix,

            // 🔑 克隆脚本数组
            componentScripts: this.componentScripts ?
                this.componentScripts.map(script => ({ ...script })) : [],
            fontSize: this.fontSize,
            fontFace: this.fontFace,
            fontBold: this.fontBold,
            fontItalic: this.fontItalic,
            textColor: this.textColor,
            outlineColor: this.outlineColor,
            outlineWidth: this.outlineWidth,
            textAlign: this.textAlign,
            verticalAlign: this.verticalAlign,
            backgroundColor: this.backgroundColor,
            backgroundOpacity: this.backgroundOpacity,

            // 🔑 间距属性
            letterSpacing: this.letterSpacing,
            wordSpacing: this.wordSpacing,
            lineHeight: this.lineHeight,
            spacingMode: this.spacingMode
        };
    }

}

// 注册LabelModel到基类容器
BaseObjectModel.registerModel('UILabel', LabelModel);
BaseObjectModel.registerModel('Label', LabelModel);
