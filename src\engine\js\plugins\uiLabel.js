

(() => {
    'use strict';
    //=============================================================================
    // UILabel Class - 专门的文本组件
    //=============================================================================

    /**
     * 文本组件类 - 继承自 Sprite
     * 专门用于显示文本，不混合其他功能
     */
    class UILabel extends Sprite {
        constructor(properties = {}) {
            super();

            console.log('🏷️ UILabel: 创建文本组件', properties);

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UILabel';

            // 🔑 使用UIComponentUtils添加UIComponent功能
            if (window.UIComponentUtils) {
                window.UIComponentUtils.applyToSprite(this, properties);
            }

            // 🔑 使用UIScriptManager添加脚本功能
            if (window.UIScriptManager) {
                window.UIScriptManager.applyToObject(this, properties);
                console.log('🔧 UILabel: 脚本系统初始化完成', {
                    hasComponentScripts: !!this.componentScripts,
                    scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                    scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                });
            }

            this.initializeLabel(properties);

            // 标记为已创建并执行onStart
            this._isCreated = true;
            if (this.executeScript) {
                this.executeScript('onStart');
            }
        }


        /**
         * 初始化文本组件
         * @param {Object} properties 文本属性
         */
        initializeLabel(properties) {
            // � 设置为非交互式，避免拦截父容器的事件
            this.interactive = false;
            this.interactiveChildren = false;

            // 设置默认属性
            this.setupDefaultProperties(properties);

            // 设置文本位图
            this.setupTextBitmap();

            // 绘制文本
            this.redrawText();

            console.log('UILabel created:', this.labelWidth, 'x', this.labelHeight);
        }

        /**
         * 设置默认属性
         */
        setupDefaultProperties(properties) {
            // 基础属性 - 如果没有传入宽高，使用默认值
            this.labelWidth = properties.width || 200;
            this.labelHeight = properties.height || 40;

            // 文本属性 - 使用内部属性存储
            this._text = properties.text || 'Label Text';
            this.prefix = properties.prefix || '';  // 前缀
            this.suffix = properties.suffix || '';  // 后缀
            this.fontSize = properties.fontSize || 16;
            this.fontFace = properties.fontFace || 'GameFont';
            this.fontBold = properties.fontBold || false;
            this.fontItalic = properties.fontItalic || false;

            // 颜色属性
            this.textColor = properties.textColor || '#ffffff';
            this.outlineColor = properties.outlineColor || '#000000';
            this.outlineWidth = properties.outlineWidth || 4;

            // 对齐属性
            this.textAlign = properties.textAlign || 'center'; // left, center, right
            this.verticalAlign = properties.verticalAlign || 'middle'; // top, middle, bottom
            // 🔧 新增：垂直偏移调节参数（可以为负数向上偏移，正数向下偏移）
            this.verticalOffset = properties.verticalOffset || 0;

            // 🔑 间距属性
            this.letterSpacing = properties.letterSpacing || 0;
            this.wordSpacing = properties.wordSpacing || 0;
            this.lineHeight = properties.lineHeight || 1.2;

            // 🔑 渲染模式
            this.spacingMode = properties.spacingMode || 'auto'; // 'auto', 'bitmap', 'canvas'

            // 背景属性
            this.backgroundColor = properties.backgroundColor || 'transparent';
            this.backgroundOpacity = properties.backgroundOpacity || 1;
        }

        /**
         * 获取标签宽度
         */
        get width() {
            return this.labelWidth;
        }

        /**
         * 设置标签宽度
         */
        set width(value) {
            this.labelWidth = value;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 获取标签高度
         */
        get height() {
            return this.labelHeight;
        }

        /**
         * 设置标签高度
         */
        set height(value) {
            this.labelHeight = value;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 设置文本位图
         */
        setupTextBitmap() {
            this.bitmap = new window.Bitmap(this.labelWidth, this.labelHeight);

            // 设置字体属性
            this.bitmap.fontSize = this.fontSize;
            this.bitmap.fontFace = this.fontFace;
            this.bitmap.fontBold = this.fontBold;
            this.bitmap.fontItalic = this.fontItalic;
            this.bitmap.textColor = this.textColor;
            this.bitmap.outlineColor = this.outlineColor;
            this.bitmap.outlineWidth = this.outlineWidth;
        }

        /**
         * 重绘文本
         */
        redrawText() {
            if (!this.bitmap) return;

            // 清除画布
            this.bitmap.clear();

            // 重新设置bitmap的所有属性（确保颜色正确）
            this.bitmap.fontSize = this.fontSize;
            this.bitmap.fontFace = this.fontFace;
            this.bitmap.fontBold = this.fontBold;
            this.bitmap.fontItalic = this.fontItalic;
            this.bitmap.textColor = this.textColor;
            this.bitmap.outlineColor = this.outlineColor;
            this.bitmap.outlineWidth = this.outlineWidth;

            console.log('🔧 UILabel: redrawText - textColor:', this.textColor, 'outlineColor:', this.outlineColor);

            // 绘制背景（如果需要）
            this.drawBackground();

            // 获取最终文本
            const finalText = this.getFinalText();

            // 根据是否需要间距选择渲染方法
            if (this.needsCustomSpacing()) {
                this.drawTextWithSpacing(finalText);
            } else {
                this.drawTextNormal(finalText);
            }
        }

        /**
         * 绘制背景
         */
        drawBackground() {
            if (this.backgroundColor !== 'transparent') {
                this.bitmap.fillRect(0, 0, this.labelWidth, this.labelHeight, this.backgroundColor);
            }
        }

        /**
         * 获取最终显示文本
         */
        getFinalText() {
            // 处理文本内容（检查是否为表达式）
            let displayText = this._text;
            if (window.EDITOR_MODE && this._text && this._text.startsWith('{{') && this._text.endsWith('}}')) {
                // 编辑器中用eval()预览表达式
                try {
                    const expression = this._text.slice(2, -2); // 去掉{{}}
                    displayText = String(eval(expression)) || this._text;
                } catch (error) {
                    console.warn('表达式预览失败:', this._text, error);
                    displayText = this._text; // 出错就显示原字符串
                }
            }

            // 添加前缀和后缀
            return this.prefix + displayText + this.suffix;
        }

        /**
         * 检查是否需要自定义间距
         */
        needsCustomSpacing() {
            return this.letterSpacing > 0 ||
                   this.wordSpacing > 0 ||
                   this.lineHeight !== 1.2;
        }

        /**
         * 使用标准方法绘制文本
         */
        drawTextNormal(finalText) {
            const textY = this.calculateTextY();

            // 🔧 修复：lineHeight 应该是字体大小，不是容器高度
            this.bitmap.drawText(
                finalText,
                0, textY,
                this.labelWidth, this.fontSize,  // 使用 fontSize 作为 lineHeight
                this.textAlign
            );
        }

        /**
         * 使用自定义间距绘制文本
         */
        drawTextWithSpacing(finalText) {
            // 🔧 临时修复：强制使用逐字符绘制，确保Y坐标一致性
            console.log('🔧 UILabel: 强制使用逐字符绘制，确保Y坐标一致');
            this.drawCharByChar(finalText);
        }

        /**
         * 尝试使用 Canvas 2D Context 的 letterSpacing
         */
        tryCanvasSpacing(text) {
            const context = this.bitmap.context;
            if (!context || !('letterSpacing' in context)) {
                return false;
            }

            try {
                // 设置字体属性
                context.font = this.buildFontString();
                context.fillStyle = this.textColor;
                context.strokeStyle = this.outlineColor;
                context.lineWidth = this.outlineWidth;

                // 🔑 设置字符间距
                context.letterSpacing = `${this.letterSpacing}px`;

                // 🔧 修复：Canvas 2D Context 基线调整
                // Canvas 2D Context 从基线绘制，Bitmap.drawText 从顶部绘制
                const topY = this.calculateTextY(); // 获取顶部位置
                const textY = topY + this.fontSize * 0.85; // 基线约在85%位置
                const textX = this.calculateStartX(text);

                // 绘制描边
                if (this.outlineWidth > 0) {
                    context.strokeText(text, textX, textY);
                }

                // 绘制填充
                context.fillText(text, textX, textY);

                return true;
            } catch (error) {
                console.warn('Canvas letterSpacing 失败:', error);
                return false;
            }
        }

        /**
         * 构建字体字符串
         */
        buildFontString() {
            let font = '';
            if (this.fontItalic) font += 'italic ';
            if (this.fontBold) font += 'bold ';
            font += `${this.fontSize}px `;
            font += this.fontFace;
            return font;
        }

        /**
         * 逐字符绘制文本
         */
        drawCharByChar(text) {
            const characters = text.split('');
            let currentX = this.calculateStartX(text);
            const textY = this.calculateTextY();

            characters.forEach((char, index) => {
                // 绘制单个字符
                // 🔧 修复：lineHeight 应该是字体大小，不是容器高度
                this.bitmap.drawText(
                    char,
                    currentX, textY,
                    this.getCharWidth(char), this.fontSize,  // 使用 fontSize 作为 lineHeight
                    'left'
                );

                // 计算下一个字符的位置
                const charWidth = this.getCharWidth(char);
                currentX += charWidth;

                // 添加字符间距
                if (index < characters.length - 1) {
                    if (char === ' ') {
                        currentX += this.wordSpacing;
                    } else {
                        currentX += this.letterSpacing;
                    }
                }
            });
        }

        /**
         * 获取字符宽度（带缓存优化）
         */
        getCharWidth(char) {
            // 创建缓存键
            const cacheKey = `${char}_${this.fontSize}_${this.fontFace}_${this.fontBold}_${this.fontItalic}`;

            // 检查缓存
            if (!this._charWidthCache) {
                this._charWidthCache = {};
            }

            if (this._charWidthCache[cacheKey] !== undefined) {
                return this._charWidthCache[cacheKey];
            }

            // 创建临时bitmap测量字符宽度
            const tempBitmap = new window.Bitmap(100, 100);
            tempBitmap.fontSize = this.fontSize;
            tempBitmap.fontFace = this.fontFace;
            tempBitmap.fontBold = this.fontBold;
            tempBitmap.fontItalic = this.fontItalic;

            const width = tempBitmap.measureTextWidth(char);

            // 缓存结果
            this._charWidthCache[cacheKey] = width;

            // 销毁临时bitmap
            tempBitmap.destroy();

            return width;
        }

        /**
         * 计算文本起始X位置
         */
        calculateStartX(text) {
            const totalWidth = this.calculateTextWidth(text);
    // 🔧 修复：为描边预留空间，避免左侧裁切
              const outlinePadding = this.outlineWidth > 0 ? Math.ceil(this.outlineWidth / 2) : 0;
            switch (this.textAlign) {
                case 'left':
                    return outlinePadding;
                case 'right':
                    return this.labelWidth - totalWidth-outlinePadding;
                case 'center':
               default:
            return Math.max(outlinePadding, (this.labelWidth - totalWidth) / 2);
            }
        }

        /**
         * 计算文本总宽度（包含间距）
         */
        calculateTextWidth(text) {
            if (!this.needsCustomSpacing()) {
                // 使用标准测量
                const tempBitmap = new window.Bitmap(1000, 100);
                tempBitmap.fontSize = this.fontSize;
                tempBitmap.fontFace = this.fontFace;
                tempBitmap.fontBold = this.fontBold;
                tempBitmap.fontItalic = this.fontItalic;
                return tempBitmap.measureTextWidth(text);
            }

            // 计算带间距的宽度
            const characters = text.split('');
            let totalWidth = 0;

            characters.forEach((char, index) => {
                totalWidth += this.getCharWidth(char);

                // 添加间距
                if (index < characters.length - 1) {
                    if (char === ' ') {
                        totalWidth += this.wordSpacing;
                    } else {
                        totalWidth += this.letterSpacing;
                    }
                }
            });

            return totalWidth;
        }



        /**
         * 计算文本Y位置（垂直对齐）
         * 🔧 修复：使用最简单直接的方法，让源码的内部计算自然工作
         */
        calculateTextY() {
            // 🔧 不再试图反推复杂的计算，直接使用简单的位置设置
            // 让 drawText 的内部逻辑自然处理文本定位

            switch (this.verticalAlign) {
                case 'top':
                    // 🔧 顶部对齐：从容器顶部开始
                    return 0 + (this.verticalOffset || 0);
                case 'bottom':
                    // 🔧 底部对齐：从容器底部减去字体大小
                    return this.labelHeight - this.fontSize + (this.verticalOffset || 0);
                case 'middle':
                default:
                    // 🔧 居中对齐：简单的居中计算
                    return (this.labelHeight - this.fontSize) / 2 + (this.verticalOffset || 0);
            }
        }

        /**
         * 🔑 text 属性的 getter
         */
        get text() {
            return this._text;
        }

        /**
         * 🔑 text 属性的 setter - 统一的文本设置方式，自动转换类型
         */
        set text(value) {
            // 🔑 自动类型转换
            let stringValue;
            if (value === null || value === undefined) {
                stringValue = '';
            } else if (typeof value === 'string') {
                stringValue = value;
            } else if (typeof value === 'number') {
                stringValue = value.toString();
            } else if (typeof value === 'boolean') {
                stringValue = value ? 'true' : 'false';
            } else if (typeof value === 'object') {
                try {
                    // 对于对象，尝试 JSON 序列化
                    stringValue = JSON.stringify(value);
                } catch (error) {
                    // 如果序列化失败，使用 toString
                    stringValue = value.toString();
                }
            } else {
                // 其他类型直接转换
                stringValue = String(value);
            }

            if (this._text !== stringValue) {
                this._text = stringValue;
                this.redrawText();
            }
        }

        /**
         * 设置文本内容（保持向后兼容）
         * @deprecated 推荐直接使用 this.text = value
         */
        setText(text) {
            this.text = text;
        }

        /**
         * 设置前缀
         */
        setPrefix(prefix) {
            this.prefix = prefix;
            this.redrawText();
        }

        /**
         * 设置后缀
         */
        setSuffix(suffix) {
            this.suffix = suffix;
            this.redrawText();
        }

        /**
         * 设置字符间距
         */
        setLetterSpacing(spacing) {
            this.letterSpacing = spacing;
            this.redrawText();
        }

        /**
         * 设置单词间距
         */
        setWordSpacing(spacing) {
            this.wordSpacing = spacing;
            this.redrawText();
        }

        /**
         * 设置行高
         */
        setLineHeight(height) {
            this.lineHeight = height;
            this.redrawText();
        }

        /**
         * 批量设置间距
         */
        setSpacing(options) {
            if (options.letter !== undefined) {
                this.letterSpacing = options.letter;
            }
            if (options.word !== undefined) {
                this.wordSpacing = options.word;
            }
            if (options.line !== undefined) {
                this.lineHeight = options.line;
            }
            this.redrawText();
        }

        /**
         * 设置字体大小
         */
        setFontSize(size) {
            this.fontSize = size;
            this.bitmap.fontSize = size;
            this._clearCharWidthCache(); // 清除字符宽度缓存
            this.redrawText();
        }

        /**
         * 清除字符宽度缓存
         */
        _clearCharWidthCache() {
            this._charWidthCache = {};
        }

        /**
         * 设置文本颜色
         */
        setTextColor(color) {
            console.log('🔧 UILabel: setTextColor调用', color);
            this.textColor = color;
            this.bitmap.textColor = color;
            console.log('🔧 UILabel: bitmap.textColor设置为', this.bitmap.textColor);
            this.redrawText();
        }

        /**
         * 设置描边颜色
         */
        setOutlineColor(color) {
            console.log('🔧 UILabel: setOutlineColor调用', color);
            this.outlineColor = color;
            this.bitmap.outlineColor = color;
            console.log('🔧 UILabel: bitmap.outlineColor设置为', this.bitmap.outlineColor);
            this.redrawText();
        }

        /**
         * 设置描边宽度
         */
        setOutlineWidth(width) {
            this.outlineWidth = width;
            this.bitmap.outlineWidth = width;
            this.redrawText();
        }

        /**
         * 设置文本对齐
         */
        setTextAlign(align) {
            this.textAlign = align;
            this.redrawText();
        }

        /**
         * 设置垂直对齐
         */
        setVerticalAlign(align) {
            this.verticalAlign = align;
            this.redrawText();
        }

        /**
         * 🔧 设置垂直偏移（用于微调文本位置）
         * @param {number} offset 偏移量，负数向上，正数向下
         */
        setVerticalOffset(offset) {
            this.verticalOffset = offset;
            this.redrawText();
        }

        /**
         * 设置尺寸
         */
        setSize(width, height) {
            this.labelWidth = width;
            this.labelHeight = height;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 设置背景颜色
         */
        setBackgroundColor(color) {
            this.backgroundColor = color;
            this.redrawText();
        }

        /**
         * 获取所有属性（用于模型同步）
         */
        getProperties() {
            return {
                name:this.name,
                // 基础属性
                x: this.x,
                y: this.y,
                width: this.labelWidth,
                height: this.labelHeight,

                // 文本属性
                text: this._text,
                fontSize: this.fontSize,
                fontFace: this.fontFace,
                fontBold: this.fontBold,
                fontItalic: this.fontItalic,

                // 颜色属性
                textColor: this.textColor,
                outlineColor: this.outlineColor,
                outlineWidth: this.outlineWidth,

                // 对齐属性
                textAlign: this.textAlign,
                verticalAlign: this.verticalAlign,

                // 背景属性
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity
            };
        }

        /**
         * 克隆当前 UILabel 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UILabel} 克隆的 UILabel 对象
         */
        clone(options = {}) {
            console.log('🔄 UILabel: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.labelWidth,
                height: this.labelHeight,
                visible: this.visible,

                // 🔑 UIComponent 属性（安全访问）
                name: this.name || '',
                enabled: this.enabled !== false,
                dataBinding: this.dataBinding || '',

                // 🔑 深度克隆组件脚本数组
                componentScripts: this.componentScripts ?
                    this.componentScripts.map(script => ({
                        id: script.id,
                        name: script.name,
                        type: script.type,
                        enabled: script.enabled,
                        code: script.code,
                        description: script.description
                    })) : [],

                // UILabel 特有属性
                text: this._text,
                prefix: this.prefix,
                suffix: this.suffix,
                fontSize: this.fontSize,
                fontFace: this.fontFace,
                fontBold: this.fontBold,
                fontItalic: this.fontItalic,
                textColor: this.textColor,
                outlineColor: this.outlineColor,
                outlineWidth: this.outlineWidth,
                textAlign: this.textAlign,
                verticalAlign: this.verticalAlign,
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity,

                // 🔑 间距属性
                letterSpacing: this.letterSpacing,
                wordSpacing: this.wordSpacing,
                lineHeight: this.lineHeight,
                spacingMode: this.spacingMode
            };

            // 2. 创建克隆对象
            const clonedLabel = new UILabel(cloneProperties);

            // 🔧 调试：验证脚本是否被克隆
            console.log('🔄 UILabel: 克隆验证', {
                原始组件名称: this.name,
                克隆组件名称: clonedLabel.name,
                原始脚本数量: this.componentScripts ? this.componentScripts.length : 0,
                克隆脚本数量: clonedLabel.componentScripts ? clonedLabel.componentScripts.length : 0,
                原始脚本详情: this.componentScripts ? this.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : [],
                克隆脚本详情: clonedLabel.componentScripts ? clonedLabel.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : []
            });

            // 3. 设置位置和变换属性
            clonedLabel.x = this.x + (offsetPosition ? offsetX : 0);
            clonedLabel.y = this.y + (offsetPosition ? offsetY : 0);
            clonedLabel.scale.x = this.scale.x;
            clonedLabel.scale.y = this.scale.y;
            clonedLabel.rotation = this.rotation;
            clonedLabel.alpha = this.alpha;
            clonedLabel.anchor.x = this.anchor.x;
            clonedLabel.anchor.y = this.anchor.y;
            clonedLabel.pivot.x = this.pivot.x;
            clonedLabel.pivot.y = this.pivot.y;
            clonedLabel.skew.x = this.skew.x;
            clonedLabel.skew.y = this.skew.y;
            clonedLabel.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedLabel.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            console.log('✅ UILabel: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedLabel;
        }
        /**
         * 🔑 获取克隆属性
         */
        getCloneProperties() {
            return {
                // 基础属性
                width: this.labelWidth,
                height: this.labelHeight,

                // 🔑 UIComponent 属性
                name: this.name || '',
                enabled: this.enabled !== false,
                dataBinding: this.dataBinding || '',
                componentScript: this.componentScript ? {
                    lifecycle: { ...(this.componentScript.lifecycle || {}) },
                    dataEvents: { ...(this.componentScript.dataEvents || {}) },
                    interactionEvents: { ...(this.componentScript.interactionEvents || {}) },
                    customFunctions: { ...(this.componentScript.customFunctions || {}) },
                    variables: { ...(this.componentScript.variables || {}) }
                } : undefined,

                // 文本属性
                text: this._text,
                prefix: this.prefix,
                suffix: this.suffix,
                fontSize: this.fontSize,
                fontFace: this.fontFace,
                fontBold: this.fontBold,
                fontItalic: this.fontItalic,

                // 颜色和样式
                textColor: this.textColor,
                outlineColor: this.outlineColor,
                outlineWidth: this.outlineWidth,

                // 对齐
                textAlign: this.textAlign,
                verticalAlign: this.verticalAlign,

                // 背景
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity,

                // 间距
                letterSpacing: this.letterSpacing || 0,
                wordSpacing: this.wordSpacing || 0,
                lineHeight: this.lineHeight || 1.2,
                spacingMode: this.spacingMode || 'auto'
            };
        }

        /**
         * 每帧更新
         */
        update() {
            super.update();

            // 🔑 执行更新脚本
            if (this.executeScript) {
                this.executeScript('onUpdate');
            }
        }

        /**
         * 销毁文本组件
         */
        destroy() {
            // 🔑 安全执行销毁脚本
            try {
                if (this.executeScript && !this._isDestroying) {
                    this._isDestroying = true; // 防止重复销毁
                    this.executeScript('onDestroy');
                }
            } catch (error) {
                console.warn('⚠️ UILabel: 销毁脚本执行失败', error);
            }

            // 清理缓存
            this._clearCharWidthCache();

            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
                try {
               if (super.destroy){
              super.destroy();
               } } catch (error) {
                  console.warn('⚠️ UILabel: 销毁脚本执行失败', error);
              }

        }
    }
    // 将类添加到全局
    window.UILabel = UILabel;

    console.log('UI Base Components Plugin loaded - UIImage and UILabel classes available');

})();