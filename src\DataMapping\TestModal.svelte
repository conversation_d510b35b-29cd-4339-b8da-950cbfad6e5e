<script lang="ts">
  import DataFlowModal from './DataFlowModal.svelte';
  import { getSupportedScenes } from './scenes';

  let isModalOpen = false;
  let selectedScene = 'Scene_Title';

  const supportedScenes = getSupportedScenes();

  function openModal() {
    isModalOpen = true;
  }

  function closeModal() {
    isModalOpen = false;
  }
</script>

<div class="test-container">
  <h1>📊 DataMapping 测试</h1>
  
  <div class="controls">
    <label>
      选择场景:
      <select bind:value={selectedScene}>
        {#each supportedScenes as scene}
          <option value={scene}>{scene}</option>
        {/each}
      </select>
    </label>
    
    <button on:click={openModal}>
      打开数据流分析
    </button>
  </div>

  <div class="info">
    <p>当前选择的场景: <strong>{selectedScene}</strong></p>
    <p>支持的场景: {supportedScenes.join(', ')}</p>
  </div>
</div>

<DataFlowModal 
  bind:isOpen={isModalOpen} 
  sceneName={selectedScene}
/>

<style>
  .test-container {
    padding: 20px;
    max-width: 600px;
    margin: 0 auto;
    font-family: Arial, sans-serif;
  }

  .controls {
    display: flex;
    gap: 16px;
    align-items: center;
    margin: 20px 0;
  }

  .controls label {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .controls select {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .controls button {
    padding: 10px 16px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .controls button:hover {
    background-color: #2980b9;
  }

  .info {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #3498db;
  }

  .info p {
    margin: 8px 0;
  }
</style>
