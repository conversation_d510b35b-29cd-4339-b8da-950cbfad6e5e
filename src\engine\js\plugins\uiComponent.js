/*:
 * @target MZ
 * @plugindesc UIComponent v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description UI组件工具函数，为Sprite添加UIComponent功能
 *
 * @help uiComponent.js
 *
 * UIComponent工具提供以下功能：
 * 1. 统一的数据绑定系统
 * 2. 脚本执行系统（生命周期、数据事件、交互事件）
 * 3. 自动的生命周期管理
 */

(() => {
    'use strict';

    /**
     * UI更新管理器
     * 使用 RPG Maker MZ 内置的更新循环来管理所有UI组件的更新
     */
    window.UIUpdateManager = {
        _components: [],
        _isHooked: false,

        /**
         * 注册组件到更新循环
         */
        register(component) {
            if (this._components.indexOf(component) === -1) {
                this._components.push(component);
                console.log(`🔧 UIUpdateManager: 注册组件 ${component.constructor.name}，总数: ${this._components.length}`);

                // 确保已挂钩到更新循环
                this._ensureUpdateHook();
            }
        },

        /**
         * 从更新循环注销组件
         */
        unregister(component) {
            const index = this._components.indexOf(component);
            if (index !== -1) {
                this._components.splice(index, 1);
                console.log(`🔧 UIUpdateManager: 注销组件 ${component.constructor.name}，总数: ${this._components.length}`);
            }
        },

        /**
         * 确保已挂钩到 RPG Maker MZ 的更新循环
         */
        _ensureUpdateHook() {
            if (this._isHooked) return;

            // 挂钩到 SceneManager 的更新循环
            if (window.SceneManager && window.SceneManager.updateMain) {
                const originalUpdate = window.SceneManager.updateMain;
                window.SceneManager.updateMain = function() {
                    // 调用原始更新
                    originalUpdate.call(this);

                    // 更新所有注册的UI组件
                    window.UIUpdateManager._updateComponents();
                };

                this._isHooked = true;
                console.log('✅ UIUpdateManager: 已挂钩到 SceneManager.updateMain');
            }
        },

        /**
         * 更新所有注册的组件
         */
        _updateComponents() {
            for (let i = this._components.length - 1; i >= 0; i--) {
                const component = this._components[i];
                try {
                    if (component && component.update && typeof component.update === 'function') {
                        component.update();
                    }
                } catch (error) {
                    console.error(`❌ UIUpdateManager: 组件更新失败`, {
                        component: component.constructor.name,
                        error: error.message
                    });

                    // 移除有问题的组件
                    this._components.splice(i, 1);
                }
            }
        }
    };

    /**
     * UIComponent工具类
     * 提供静态方法为Sprite添加UIComponent功能
     */
    window.UIComponentUtils = {
        /**
         * 为Sprite添加UIComponent功能
         */
        applyToSprite(sprite, properties = {}) {
            console.log(`🔧 UIComponent: 为${sprite.constructor.name}添加UIComponent功能`, properties);
            
            // 🔑 组件标识
            sprite.isUIComponent = true;
            sprite.uiComponentType = sprite.constructor.name;
            sprite.componentId = properties.id || this.generateComponentId(sprite);
            
            // 🔑 基础属性
            sprite.name = properties.name || '';
            sprite.enabled = properties.enabled !== false;
            
            // 🔑 数据绑定系统（简化）
            sprite.dataBinding = properties.dataBinding || '';
            sprite._dataWatcher = null;
            sprite._isDataBound = false;
            
            // 🔑 脚本执行控制
            sprite._scriptExecutionEnabled = properties.scriptExecutionEnabled !== false;

            // 🔑 更新循环控制
            sprite._updateEnabled = properties.updateEnabled !== false;
            sprite._lastUpdateTime = Date.now();
            sprite._isRegisteredForUpdate = false;
            
            // 🔑 生命周期状态
            sprite._isCreated = false;
            sprite._isStarted = false;
            sprite._isDestroyed = false;

            // 🔑 拖拽重排序状态标记
            sprite._isBeingReordered = false;
            

            
            // 🔑 初始化数据监听器数组
            sprite._dataWatchers = [];

            // 添加方法
            this.addMethods(sprite);

            return sprite;
        },
        
        /**
         * 生成组件ID
         */
        generateComponentId(sprite) {
            return `${sprite.constructor.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        },
        

        




        /**
         * 为sprite添加方法
         */
        addMethods(sprite) {
            // 🔑 智能数据路径解析器
            sprite.parseDataPath = function(dataPath) {
                // 支持简化语法
                const pathMappings = {
                    // 简化语法 -> 实际路径
                    '$gameParty._gold': { object: () => window.$gameParty, field: '_gold' },
                    '$gameVariables[': {
                        pattern: /^\$gameVariables\[(\d+)\]$/,
                        resolver: (match) => ({
                            object: () => window.$gameVariables._dataClass,
                            field: match[1]
                        })
                    },
                    '$gameSwitches[': {
                        pattern: /^\$gameSwitches\[(\d+)\]$/,
                        resolver: (match) => ({
                            object: () => window.$gameSwitches._dataClass,
                            field: match[1]
                        })
                    },
                    '$gameActors[': {
                        pattern: /^\$gameActors\[(\d+)\]\._exp$/,
                        resolver: (match) => ({
                            object: () => window.$gameActors._data[match[1]],
                            field: '_exp'
                        })
                    }
                };

                // 直接匹配
                if (pathMappings[dataPath]) {
                    return pathMappings[dataPath];
                }

                // 模式匹配
                for (const [key, mapping] of Object.entries(pathMappings)) {
                    if (mapping.pattern && mapping.resolver) {
                        const match = dataPath.match(mapping.pattern);
                        if (match) {
                            return mapping.resolver(match);
                        }
                    }
                }

                // 兜底：简单的点分割解析
                const lastDotIndex = dataPath.lastIndexOf('.');
                if (lastDotIndex > 0) {
                    const objectPath = dataPath.substring(0, lastDotIndex);
                    const fieldName = dataPath.substring(lastDotIndex + 1);

                    try {
                        return {
                            object: () => eval(objectPath),
                            field: fieldName
                        };
                    } catch (error) {
                        console.warn('🚨 UIComponent: 无法解析数据路径:', dataPath, error);
                        return null;
                    }
                }

                return null;
            };

            // 🔑 新的数据监听API - watchData (支持自定义对象)
            sprite.watchData = function(target, callback) {
                console.log('🔧 UIComponent: 设置数据监听', { target, hasCallback: !!callback });

                if (typeof callback !== 'function') {
                    console.warn('🚨 UIComponent: watchData 回调函数无效', { target, callback });
                    return null;
                }

                try {
                    // 🔑 支持多种监听方式
                    if (typeof target === 'string') {
                        // 字符串路径：监听全局对象属性
                        return this.watchObjectProperty(target, callback);
                    } else if (typeof target === 'object' && target !== null) {
                        // 对象：监听对象的所有属性变化
                        return this.watchObjectChanges(target, callback);
                    } else {
                        console.warn('🚨 UIComponent: 不支持的监听目标类型:', typeof target);
                        return null;
                    }

                } catch (error) {
                    console.error('❌ UIComponent: 设置数据监听失败', error);
                    return null;
                }
            };

            // 🔑 监听对象属性（字符串路径）
            sprite.watchObjectProperty = function(dataPath, callback) {
                // 使用智能解析器
                const parsed = this.parseDataPath(dataPath);
                if (!parsed) {
                    console.warn('🚨 UIComponent: 无法解析数据路径:', dataPath);
                    return null;
                }

                const targetObject = parsed.object();
                const fieldName = parsed.field;

                if (!targetObject) {
                    console.warn('🚨 UIComponent: 目标对象不存在:', dataPath);
                    return null;
                }

                console.log('🎯 UIComponent: 设置对象属性监听', {
                    path: dataPath,
                    object: targetObject.constructor?.name || 'Unknown',
                    field: fieldName,
                    currentValue: targetObject[fieldName]
                });

                // 使用ProxyFieldWatcher监听字段变化
                if (window.ProxyFieldWatcher) {
                    const watcher = window.ProxyFieldWatcher.watch(
                        targetObject,
                        fieldName,
                        (field, newValue, oldValue) => {
                            console.log(`🔄 UIComponent: 对象属性变化 ${dataPath}`, {
                                field,
                                newValue,
                                oldValue
                            });

                            // 调用用户回调
                            try {
                                callback(newValue, oldValue);
                            } catch (error) {
                                console.error('❌ UIComponent: 数据监听回调执行失败', error);
                            }
                        }
                    );

                    // 添加到监听器数组
                    this._dataWatchers = this._dataWatchers || [];
                    this._dataWatchers.push({
                        path: dataPath,
                        watcher: watcher,
                        callback: callback
                    });

                    console.log('✅ UIComponent: 对象属性监听设置成功', {
                        path: dataPath,
                        totalWatchers: this._dataWatchers.length
                    });

                    return watcher;
                } else {
                    console.warn('🚨 UIComponent: ProxyFieldWatcher未加载');
                    return null;
                }
            };

            // 🔑 监听对象变化（对象引用）
            sprite.watchObjectChanges = function(targetObject, callback) {
                console.log('🎯 UIComponent: 设置对象变化监听', {
                    object: targetObject.constructor?.name || 'Unknown',
                    keys: Object.keys(targetObject)
                });

                if (!window.ProxyFieldWatcher) {
                    console.warn('🚨 UIComponent: ProxyFieldWatcher未加载');
                    return null;
                }

                // 🔑 创建一个代理对象来监听所有属性变化
                const watchers = [];

                // 监听对象的所有可枚举属性
                for (const key of Object.keys(targetObject)) {
                    console.log(`🔧 UIComponent: 为属性 ${key} 设置监听器，当前值:`, targetObject[key]);

                    const watcher = window.ProxyFieldWatcher.watch(
                        targetObject,
                        key,
                        (field, newValue, oldValue) => {
                            console.log(`🔄 UIComponent: 对象属性变化 ${key}`, {
                                field,
                                newValue,
                                oldValue
                            });

                            // 调用用户回调，传递属性名
                            try {
                                callback(newValue, oldValue, key);
                            } catch (error) {
                                console.error('❌ UIComponent: 对象监听回调执行失败', error);
                            }
                        }
                    );

                    if (watcher) {
                        watchers.push(watcher);
                        console.log(`✅ UIComponent: 属性 ${key} 监听器设置成功`);
                    } else {
                        console.warn(`🚨 UIComponent: 属性 ${key} 监听器设置失败`);
                    }
                }

                // 添加到监听器数组
                this._dataWatchers = this._dataWatchers || [];
                this._dataWatchers.push({
                    path: `[Object:${targetObject.constructor?.name || 'Unknown'}]`,
                    watcher: {
                        unwatch: () => watchers.forEach(w => w.unwatch && w.unwatch())
                    },
                    callback: callback
                });

                console.log('✅ UIComponent: 对象变化监听设置成功', {
                    watchedProperties: Object.keys(targetObject),
                    successfulWatchers: watchers.length,
                    totalWatchers: this._dataWatchers.length
                });

                return {
                    unwatch: () => watchers.forEach(w => w.unwatch && w.unwatch())
                };
            };

            // 🔑 重排序状态管理方法
            sprite.markAsBeingReordered = function() {
                this._isBeingReordered = true;
                console.log('🔄 UIComponent: 标记为重排序状态', this.constructor.name);
            };

            sprite.unmarkAsBeingReordered = function() {
                this._isBeingReordered = false;
                console.log('🔄 UIComponent: 取消重排序状态', this.constructor.name);
            };

            sprite.isBeingReordered = function() {
                return this._isBeingReordered === true;
            };

            // 🔑 统一的字段更新方法 - 只更新自身
            sprite.onFieldUpdate = function(ctx) {
                console.log('� UIComponent: 字段更新触发', {
                    componentId: this.componentId,
                    ctx: ctx
                });

                // 执行脚本中的 onFieldUpdate 方法
                if (this.executeScript && typeof this.executeScript === 'function') {
                    try {
                        console.log(`🔧 UIComponent: 执行 onFieldUpdate 脚本`, this.componentId);
                        this.executeScript('onFieldUpdate', ctx);
                        console.log(`✅ UIComponent: onFieldUpdate 脚本执行完成`, this.componentId);
                    } catch (error) {
                        console.error(`❌ UIComponent: onFieldUpdate 脚本执行失败`, {
                            componentId: this.componentId,
                            error: error.message,
                            stack: error.stack
                        });
                    }
                }
            };

            // 🔑 级联更新方法 - 更新自身和所有子对象
            sprite.updateFieldsToChildren = function(ctx) {
                console.log('� UIComponent: 级联字段更新开始', {
                    componentId: this.componentId,
                    ctx: ctx
                });

                // 1. 先触发自身的 onFieldUpdate
                if (typeof this.onFieldUpdate === 'function') {
                    this.onFieldUpdate(ctx);
                }

                // 2. 然后触发所有子对象的 onFieldUpdate
                if (this.children && Array.isArray(this.children)) {
                    this.children.forEach((child, index) => {
                        if (child && typeof child.onFieldUpdate === 'function') {
                            try {
                                console.log(`🔧 UIComponent: 通知子组件 ${index} 字段更新`);
                                child.onFieldUpdate(ctx);
                            } catch (error) {
                                console.error(`❌ UIComponent: 子组件 ${index} 字段更新失败`, error);
                            }
                        }
                    });
                }

                console.log('✅ UIComponent: 级联字段更新完成', this.componentId);
            };


            // 生命周期方法
            sprite.onAddedToParent = function() {
                const self = this;
                console.log('🔧 UIComponent: 组件被添加到父容器', self.constructor.name);

                if (!self._isStarted && self._isCreated) {
                    self._isStarted = true;
                    if (self.executeScript && typeof self.executeScript === 'function') {
                        self.executeScript('onStart');
                    }

                    // 🔑 启动更新循环
                    if (self.startUpdateLoop && typeof self.startUpdateLoop === 'function') {
                        self.startUpdateLoop();
                    }

                    // 启动子组件
                    if (self.children) {
                        self.children.forEach(child => {
                            if (child.isUIComponent && !child._isStarted) {
                                child.onAddedToParent();
                            }
                        });
                    }
                }




            };

            // 🔑 清理所有数据监听器
            sprite.clearAllDataWatchers = function() {
                console.log('🔧 UIComponent: 清理所有数据监听器');

                if (this._dataWatchers && this._dataWatchers.length > 0) {
                    this._dataWatchers.forEach((watcherInfo, index) => {
                        try {
                            if (watcherInfo.watcher && watcherInfo.watcher.unwatch) {
                                watcherInfo.watcher.unwatch();
                                console.log(`✅ UIComponent: 数据监听器已清理 ${watcherInfo.path}`);
                            }
                        } catch (error) {
                            console.error(`❌ UIComponent: 清理数据监听器失败 ${watcherInfo.path}`, error);
                        }
                    });

                    this._dataWatchers = [];
                    console.log('✅ UIComponent: 所有数据监听器已清理');
                } else {
                    console.log('ℹ️ UIComponent: 没有需要清理的数据监听器');
                }
            };

            sprite.onRemovedFromParent = function() {
                const self = this;
                console.log('🔧 UIComponent: 组件从父容器移除', self.constructor.name);

                // 🔑 检查是否在重排序过程中
                if (self._isBeingReordered) {
                    console.log('🔄 UIComponent: 跳过销毁逻辑 - 正在重排序', self.constructor.name);
                    return; // 重排序时不执行销毁逻辑
                }

                // 🔑 执行销毁脚本
                if (self.executeScript && typeof self.executeScript === 'function') {
                    self.executeScript('onDestroy');
                }

                // 🔑 停止更新循环
                if (self.stopUpdateLoop && typeof self.stopUpdateLoop === 'function') {
                    self.stopUpdateLoop();
                }

                // 🔑 清理所有数据监听器
                if (self.clearAllDataWatchers) {
                    self.clearAllDataWatchers();
                }

                // 🔑 统一调用对象的 destroy 方法（如果存在且未被调用过）
                if (typeof self.destroy === 'function' && !self._isDestroyed) {
                    self._isDestroyed = true; // 防止重复调用
                    console.log('🗑️ UIComponent: 统一调用对象的 destroy 方法', self.constructor.name);

                    // 延迟调用，确保当前的 removed 事件处理完成
                    setTimeout(() => {
                        if (typeof self.destroy === 'function') {
                            self.destroy();
                        }
                    }, 0);
                }
            };

            // 🔑 更新方法
            sprite.update = function() {
                if (!this._updateEnabled || !this._isStarted) return;

                const currentTime = Date.now();
                const deltaTime = this._lastUpdateTime ? currentTime - this._lastUpdateTime : 16;
                this._lastUpdateTime = currentTime;

                // 执行 onUpdate 脚本
                if (this.executeScript && typeof this.executeScript === 'function') {
                    this.executeScript('onUpdate', { deltaTime, currentTime });
                }
            };

            // 🔑 注册到全局更新管理器
            sprite.startUpdateLoop = function() {
                if (this._isRegisteredForUpdate) return; // 避免重复注册

                // 注册到全局更新管理器
                if (window.UIUpdateManager) {
                    window.UIUpdateManager.register(this);
                    this._isRegisteredForUpdate = true;
                }
            };

            // 🔑 从全局更新管理器注销
            sprite.stopUpdateLoop = function() {
                if (!this._isRegisteredForUpdate) return;

                // 从全局更新管理器注销
                if (window.UIUpdateManager) {
                    window.UIUpdateManager.unregister(this);
                    this._isRegisteredForUpdate = false;
                }
            };

            // 事件绑定 - 使用更准确的事件名称
            sprite.on('added', sprite.onAddedToParent.bind(sprite));
            sprite.on('removed', sprite.onRemovedFromParent.bind(sprite));
        }
    };

    // 🔑 为 PIXI.Container 原型添加 UIComponent 方法，确保嵌套容器也能参与调用链
    if (window.PIXI && window.PIXI.Container && window.PIXI.Container.prototype) {
        console.log('🔧 UIComponent: 为 PIXI.Container 原型添加 UIComponent 方法');

        // 添加 onFieldUpdate 方法（如果不存在）
        if (!window.PIXI.Container.prototype.onFieldUpdate) {
            window.PIXI.Container.prototype.onFieldUpdate = function(ctx) {
                console.log('🔄 PIXI.Container: onFieldUpdate 被调用', {
                    containerName: this.name || 'unnamed',
                    hasChildren: !!(this.children && this.children.length > 0),
                    childrenCount: this.children ? this.children.length : 0,
                    ctx: ctx
                });

                // 不执行脚本（因为普通 Container 没有脚本系统）
                // 但是会传递给子对象
                if (this.children && Array.isArray(this.children)) {
                    this.children.forEach((child, index) => {
                        if (child && typeof child.onFieldUpdate === 'function') {
                            try {
                                console.log(`🔧 PIXI.Container: 通知子组件 ${index} 字段更新`);
                                child.onFieldUpdate(ctx);
                            } catch (error) {
                                console.error(`❌ PIXI.Container: 子组件 ${index} 字段更新失败`, error);
                            }
                        }
                    });
                }
            };
        }

        // 添加 updateFieldsToChildren 方法（如果不存在）
        if (!window.PIXI.Container.prototype.updateFieldsToChildren) {
            window.PIXI.Container.prototype.updateFieldsToChildren = function(ctx) {
                console.log('🔄 PIXI.Container: updateFieldsToChildren 被调用', {
                    containerName: this.name || 'unnamed',
                    hasChildren: !!(this.children && this.children.length > 0),
                    childrenCount: this.children ? this.children.length : 0,
                    ctx: ctx
                });

                // 1. 先触发自身的 onFieldUpdate（如果有的话）
                if (typeof this.onFieldUpdate === 'function') {
                    this.onFieldUpdate(ctx);
                }

                // 2. 然后触发所有子对象的 onFieldUpdate
                if (this.children && Array.isArray(this.children)) {
                    this.children.forEach((child, index) => {
                        if (child && typeof child.onFieldUpdate === 'function') {
                            try {
                                console.log(`🔧 PIXI.Container: 通知子组件 ${index} 字段更新`);
                                child.onFieldUpdate(ctx);
                            } catch (error) {
                                console.error(`❌ PIXI.Container: 子组件 ${index} 字段更新失败`, error);
                            }
                        }
                    });
                }
            };
        }
 // 🔑 添加 clone 方法（如果不存在）- 为 UILayout 模板功能提供支持
        if (!window.PIXI.Container.prototype.clone) {
            window.PIXI.Container.prototype.clone = function(options = {}) {
                console.log('🔄 PIXI.Container: clone 被调用', {
                    containerName: this.name || 'unnamed',
                    hasChildren: !!(this.children && this.children.length > 0),
                    childrenCount: this.children ? this.children.length : 0,
                    options: options
                });

                const {
                    offsetPosition = true,
                    offsetX = 20,
                    offsetY = 20
                } = options;

                // 创建新的 Container
                const cloned = new window.PIXI.Container();

                // 复制基本属性
                cloned.name = this.name ? `${this.name}_cloned` : 'cloned_container';
                cloned.visible = this.visible;
                cloned.alpha = this.alpha;
                cloned.rotation = this.rotation;
                cloned.scale.set(this.scale.x, this.scale.y);
                cloned.skew.set(this.skew.x, this.skew.y);
                cloned.pivot.set(this.pivot.x, this.pivot.y);

                // 设置位置
                if (offsetPosition) {
                    cloned.x = this.x + offsetX;
                    cloned.y = this.y + offsetY;
                } else {
                    cloned.x = this.x;
                    cloned.y = this.y;
                }

                // 递归克隆所有子对象
                if (this.children && this.children.length > 0) {
                    this.children.forEach(child => {
                        if (child && typeof child.clone === 'function') {
                            try {
                                const clonedChild = child.clone({ offsetPosition: false });
                                cloned.addChild(clonedChild);
                            } catch (error) {
                                console.error('❌ PIXI.Container: 克隆子对象失败', error);
                            }
                        } else {
                            console.warn('⚠️ PIXI.Container: 子对象没有 clone 方法，跳过', child.constructor.name);
                        }
                    });
                }

                console.log('✅ PIXI.Container: 克隆完成', {
                    originalName: this.name,
                    clonedName: cloned.name,
                    originalChildren: this.children ? this.children.length : 0,
                    clonedChildren: cloned.children ? cloned.children.length : 0
                });

                return cloned;
            };
        }
        console.log('✅ UIComponent: PIXI.Container 原型方法添加完成');
    } else {
        console.warn('⚠️ UIComponent: PIXI.Container 不可用，跳过原型方法添加');
    }

    console.log('✅ UIComponent Plugin loaded - UIComponent functionality available');

})();
