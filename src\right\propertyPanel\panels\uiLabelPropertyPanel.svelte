<script lang="ts">
  import type { LabelModel } from '../../../type/ui/labelModel.svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import LabelInput from '../../../components/LabelInput.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import ColorPicker from '../../../components/ColorPicker.svelte';
  import SafeInput from '../../../components/SafeInput.svelte';
  import Select from '../../../components/Select.svelte';


  interface Props {
    model: LabelModel;
  }

  let { model }: Props = $props();
  // 选项
  const textAlignOptions = [
    { value: 'left', label: '左对齐' },
    { value: 'center', label: '居中' },
    { value: 'right', label: '右对齐' }
  ];

  const verticalAlignOptions = [
    { value: 'top', label: '顶部' },
    { value: 'middle', label: '中间' },
    { value: 'bottom', label: '底部' }
  ];
</script>

<AccordionPanel
  title="UI Label 属性"
  icon="📝"
  badge="文本组件"
  badgeVariant="info"
  expanded={true}
>
  <!-- 文本内容 -->
  <PropertyContainer>
    <Label text="文本:" />
    <SafeInput
      bind:value={model.text}
      placeholder="输入文本内容"
      class="text-input"
    />
  </PropertyContainer>



  <!-- 前缀和后缀 -->
  <PropertyContainer>
    <Label text="前缀:" />
    <SafeInput
      bind:value={model.prefix}
      placeholder="前缀"
    />
        <Label text="后缀:" />
    <SafeInput
      bind:value={model.suffix}
      placeholder="后缀"
    />
  </PropertyContainer>
  <PropertyContainer>
    <Label text="样式:" />
    <div class="checkbox-group">
      <Checkbox
        bind:checked={model.fontBold}
        label="粗体"
        targetObject={model}
        fieldName="fontBold"
        name="字体粗体"
      />
      <Checkbox
        bind:checked={model.fontItalic}
        label="斜体"
        targetObject={model}
        fieldName="fontItalic"
        name="字体斜体"
      />
    </div>
  </PropertyContainer>

  <!-- 对齐设置 -->
  <PropertyContainer>
    <Label text="水平:" />
    <Select
      bind:value={model.textAlign}
      options={textAlignOptions}
      placeholder="选择水平对齐"
      size="sm"
      searchable={false}
      clearable={false}
      targetObject={model}
      fieldName="textAlign"
      name="水平对齐"
    />
        <Label text="垂直:" />
    <Select
      bind:value={model.verticalAlign}
      options={verticalAlignOptions}
      placeholder="选择垂直对齐"
      size="sm"
      searchable={false}
      clearable={false}
      targetObject={model}
      fieldName="verticalAlign"
      name="垂直对齐"
    />
  </PropertyContainer>
  <!-- 颜色设置 -->
  <PropertyContainer>
    <Label text="文本颜色:" />
    <ColorPicker
      bind:value={model.textColor}
      label=""
      targetObject={model}
      fieldName="textColor"
      name="文本颜色"
      onChange={(color) => {
        console.log('🔴 文本颜色变化:', color);
        model.textColor = color;
      }}
    />
        <Label text="文本大小:" />
    <LabelInput
      bind:value={model.fontSize}
      type="number"
      min={8}
      max={72}
      step={1}
      targetObject={model}
      fieldName="fontSize"
      name="字体大小"
    />
  </PropertyContainer>

  <!-- 描边设置 - 演示嵌套PropertyContainer -->
  <PropertyContainer>
    
      <Label text="描边颜色:" />
      <ColorPicker
        bind:value={model.outlineColor}
        label=""
        targetObject={model}
        fieldName="outlineColor"
        name="描边颜色"
        onChange={(color) => {
          console.log('🔵 描边颜色变化:', color);
          model.outlineColor = color;
        }}
      />
      <Label text="描边宽度:" />
      <LabelInput
        bind:value={model.outlineWidth}
        type="number"
        min={0}
        max={10}
        step={1}
        targetObject={model}
        fieldName="outlineWidth"
        name="描边宽度"
      />

  </PropertyContainer>

  <!-- 🔑 间距设置 -->
  <PropertyContainer>
    <Label text="字符间距:" />
    <LabelInput
      bind:value={model.letterSpacing}
      type="number"
      min={0}
      max={20}
      step={0.5}
      targetObject={model}
      fieldName="letterSpacing"
      name="字符间距"
    />

    <Label text="单词间距:" />
    <LabelInput
      bind:value={model.wordSpacing}
      type="number"
      min={0}
      max={50}
      step={1}
      targetObject={model}
      fieldName="wordSpacing"
      name="单词间距"
    />

    <Label text="行高:" />
    <LabelInput
      bind:value={model.lineHeight}
      type="number"
      min={0.5}
      max={3}
      step={0.1}
      targetObject={model}
      fieldName="lineHeight"
      name="行高"
    />
  </PropertyContainer>



</AccordionPanel>




<style>
  :global(.text-input) {
    flex: 1;
  }



  .checkbox-group {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  /* 覆盖Checkbox组件的字体大小 */
  :global(.simple-checkbox .checkbox-label) {
    font-size: 10px !important;
  }


</style>
