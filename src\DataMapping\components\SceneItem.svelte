<script lang="ts">
  import type { SceneDataFlow } from '../types';

  export let sceneData: SceneDataFlow;

  // 为不同类型的方法添加图标和颜色
  function getMethodInfo(method: string) {
    if (method.includes('DataManager')) {
      return { icon: '💾', color: '#10b981', bgColor: '#ecfdf5', type: '数据管理' };
    } else if (method.includes('SceneManager')) {
      return { icon: '🎬', color: '#3b82f6', bgColor: '#eff6ff', type: '场景切换' };
    } else if (method.includes('$game')) {
      return { icon: '🎮', color: '#8b5cf6', bgColor: '#f3e8ff', type: '游戏对象' };
    } else if (method.includes('AudioManager') || method.includes('SoundManager')) {
      return { icon: '🔊', color: '#f59e0b', bgColor: '#fffbeb', type: '音频' };
    } else if (method.includes('ImageManager')) {
      return { icon: '🖼️', color: '#06b6d4', bgColor: '#ecfeff', type: '图像' };
    } else {
      return { icon: '⚙️', color: '#6b7280', bgColor: '#f9fafb', type: '其他' };
    }
  }

  // 按类型分组方法
  function groupMethodsByType(methods: string[]) {
    const groups: Record<string, string[]> = {};
    methods.forEach(method => {
      const info = getMethodInfo(method);
      if (!groups[info.type]) {
        groups[info.type] = [];
      }
      groups[info.type].push(method);
    });
    return groups;
  }
</script>

<!-- 场景卡片容器 -->
<div class="scene-container">
  <!-- 场景标题卡片 -->
  <div class="scene-header-card">
    <div class="scene-title">
      <div class="scene-icon">🎯</div>
      <div class="scene-info">
        <h2>{sceneData.sceneName}</h2>
        <p class="scene-description">包含 {sceneData.buttons.length} 个交互按钮</p>
      </div>
    </div>
    <div class="scene-stats">
      <div class="stat-item">
        <span class="stat-number">{sceneData.buttons.length}</span>
        <span class="stat-label">按钮</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">{sceneData.buttons.reduce((sum, btn) => sum + btn.triggerMethods.length, 0)}</span>
        <span class="stat-label">方法</span>
      </div>
    </div>
  </div>

  <!-- 按钮卡片网格 -->
  <div class="buttons-grid">
    {#each sceneData.buttons as button}
      <div class="button-card">
        <!-- 按钮标题 -->
        <div class="button-header">
          <div class="button-title">
            <span class="button-icon">🔘</span>
            <h3>{button.buttonName}</h3>
          </div>
          <div class="method-count-badge">
            {button.triggerMethods.length}
          </div>
        </div>

        <!-- 方法分组 -->
        <div class="methods-container">
          {#each Object.entries(groupMethodsByType(button.triggerMethods)) as [type, methods]}
            {@const typeInfo = getMethodInfo(methods[0])}
            <div class="method-group">
              <div class="method-type-header" style="background-color: {typeInfo.bgColor}; color: {typeInfo.color};">
                <span class="type-icon">{typeInfo.icon}</span>
                <span class="type-name">{type}</span>
                <span class="type-count">({methods.length})</span>
              </div>
              <div class="method-list">
                {#each methods as method}
                  {@const methodInfo = getMethodInfo(method)}
                  <div class="method-item" style="border-left-color: {methodInfo.color};">
                    <code>{method}</code>
                  </div>
                {/each}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .scene-container {
    max-width: 100%;
    margin: 0 auto;
  }

  /* 场景标题卡片 */
  .scene-header-card {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-4);
    box-shadow: var(--shadow-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .scene-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
  }

  .scene-icon {
    font-size: var(--font-size-xl);
    background: var(--theme-primary-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-2);
  }

  .scene-info h2 {
    margin: 0 0 var(--spacing-1) 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
  }

  .scene-description {
    margin: 0;
    font-size: var(--font-size-sm);
    opacity: 0.9;
  }

  .scene-stats {
    display: flex;
    gap: var(--spacing-2);
  }

  .stat-item {
    text-align: center;
    background: var(--theme-primary-light);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-2) var(--spacing-3);
    min-width: 50px;
  }

  .stat-number {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 600;
    line-height: 1;
  }

  .stat-label {
    display: block;
    font-size: var(--font-size-xs);
    opacity: 0.8;
    margin-top: var(--spacing-1);
  }

  /* 按钮卡片网格 */
  .buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-3);
  }

  .button-card {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-3);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
    position: relative;
  }

  .button-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--theme-primary);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
  }

  .button-card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    border-color: var(--theme-primary);
  }

  /* 按钮标题 */
  .button-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-3);
  }

  .button-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .button-icon {
    font-size: var(--font-size-md);
  }

  .button-title h3 {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--theme-text);
  }

  .method-count-badge {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    min-width: 20px;
    text-align: center;
  }

  /* 方法分组 */
  .methods-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .method-group {
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    border: 1px solid var(--theme-border);
  }

  .method-type-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border-bottom: 1px solid var(--theme-border);
  }

  .type-icon {
    font-size: var(--font-size-sm);
  }

  .type-name {
    flex: 1;
  }

  .type-count {
    font-size: var(--font-size-xs);
    opacity: 0.8;
  }

  .method-list {
    background: var(--theme-surface-elevated);
    padding: var(--spacing-2);
  }

  .method-item {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-left: 3px solid var(--theme-primary);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-2);
    margin-bottom: var(--spacing-1);
    transition: all 0.2s ease;
  }

  .method-item:last-child {
    margin-bottom: 0;
  }

  .method-item:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-primary);
    transform: translateX(2px);
  }

  .method-item code {
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    color: var(--theme-text);
    background: none;
    padding: 0;
    font-weight: 500;
  }
</style>
