<script lang="ts">
  import type { SceneDataFlow } from '../types';

  export let sceneData: SceneDataFlow;
  export let isSelected = false;

  // 为不同类型的方法添加图标和颜色
  function getMethodInfo(method: string) {
    if (method.includes('DataManager')) {
      return { icon: '💾', color: 'var(--theme-success)', type: '数据管理' };
    } else if (method.includes('SceneManager')) {
      return { icon: '🎬', color: 'var(--theme-primary)', type: '场景切换' };
    } else if (method.includes('$game')) {
      return { icon: '🎮', color: 'var(--theme-accent)', type: '游戏对象' };
    } else if (method.includes('AudioManager') || method.includes('SoundManager')) {
      return { icon: '🔊', color: 'var(--theme-warning)', type: '音频' };
    } else if (method.includes('ImageManager')) {
      return { icon: '🖼️', color: 'var(--theme-info)', type: '图像' };
    } else {
      return { icon: '⚙️', color: 'var(--theme-text-secondary)', type: '其他' };
    }
  }

  // 按类型分组方法
  function groupMethodsByType(methods: string[]) {
    const groups: Record<string, string[]> = {};
    methods.forEach(method => {
      const info = getMethodInfo(method);
      if (!groups[info.type]) {
        groups[info.type] = [];
      }
      groups[info.type].push(method);
    });
    return groups;
  }
</script>

<div class="scene-item" class:selected={isSelected}>
  <!-- 场景标题 -->
  <div class="scene-header">
    <div class="scene-title">
      <span class="scene-icon">🎯</span>
      <h3>{sceneData.sceneName}</h3>
    </div>
    <div class="scene-stats">
      <span class="stat-badge">{sceneData.buttons.length} 个按钮</span>
    </div>
  </div>

  <!-- 按钮列表 -->
  <div class="buttons-container">
    {#each sceneData.buttons as button}
      <div class="button-card">
        <!-- 按钮名称 -->
        <div class="button-header">
          <span class="button-icon">🔘</span>
          <span class="button-name">{button.buttonName}</span>
          <span class="method-count">{button.triggerMethods.length}</span>
        </div>

        <!-- 方法分组显示 -->
        <div class="methods-container">
          {#each Object.entries(groupMethodsByType(button.triggerMethods)) as [type, methods]}
            <div class="method-group">
              <div class="method-type">
                <span class="type-icon">{getMethodInfo(methods[0]).icon}</span>
                <span class="type-name">{type}</span>
              </div>
              <div class="method-list">
                {#each methods as method}
                  <div 
                    class="method-item"
                    style="border-left-color: {getMethodInfo(method).color}"
                  >
                    <code>{method}</code>
                  </div>
                {/each}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .scene-item {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-4);
    transition: all 0.3s ease;
  }

  .scene-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--theme-primary-light);
  }

  .scene-item.selected {
    border-color: var(--theme-primary);
    box-shadow: var(--shadow-lg);
    background: var(--theme-surface-elevated);
  }

  /* 场景标题区域 */
  .scene-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--theme-border);
  }

  .scene-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .scene-icon {
    font-size: var(--font-size-lg);
  }

  .scene-title h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text);
  }

  .scene-stats {
    display: flex;
    gap: var(--spacing-2);
  }

  .stat-badge {
    background: var(--theme-primary-light);
    color: var(--theme-primary-dark);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
  }

  /* 按钮容器 */
  .buttons-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .button-card {
    background: var(--theme-surface-elevated);
    border: 1px solid var(--theme-border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-3);
    transition: all 0.2s ease;
  }

  .button-card:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-border);
  }

  /* 按钮标题 */
  .button-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-3);
  }

  .button-icon {
    font-size: var(--font-size-md);
  }

  .button-name {
    font-weight: 600;
    color: var(--theme-text);
    font-size: var(--font-size-md);
    flex: 1;
  }

  .method-count {
    background: var(--theme-accent-light);
    color: var(--theme-accent-dark);
    padding: 2px var(--spacing-1);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    min-width: 20px;
    text-align: center;
  }

  /* 方法分组 */
  .methods-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .method-group {
    background: var(--theme-surface);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
  }

  .method-type {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--theme-surface-elevated);
    border-bottom: 1px solid var(--theme-border-light);
  }

  .type-icon {
    font-size: var(--font-size-sm);
  }

  .type-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--theme-text-secondary);
  }

  .method-list {
    padding: var(--spacing-2);
  }

  .method-item {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border-light);
    border-left: 3px solid var(--theme-primary);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-2);
    margin-bottom: var(--spacing-1);
    transition: all 0.2s ease;
  }

  .method-item:last-child {
    margin-bottom: 0;
  }

  .method-item:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-border);
  }

  .method-item code {
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    color: var(--theme-text);
    background: none;
    padding: 0;
  }
</style>
