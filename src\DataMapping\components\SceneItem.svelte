<script lang="ts">
  import type { SceneDataFlow } from '../types';

  export let sceneData: SceneDataFlow;

  // 为不同类型的方法添加图标和颜色
  function getMethodInfo(method: string) {
    if (method.includes('DataManager')) {
      return { icon: '💾', color: '#10b981', bgColor: '#ecfdf5', type: '数据管理' };
    } else if (method.includes('SceneManager')) {
      return { icon: '🎬', color: '#3b82f6', bgColor: '#eff6ff', type: '场景切换' };
    } else if (method.includes('$game')) {
      return { icon: '🎮', color: '#8b5cf6', bgColor: '#f3e8ff', type: '游戏对象' };
    } else if (method.includes('AudioManager') || method.includes('SoundManager')) {
      return { icon: '🔊', color: '#f59e0b', bgColor: '#fffbeb', type: '音频' };
    } else if (method.includes('ImageManager')) {
      return { icon: '🖼️', color: '#06b6d4', bgColor: '#ecfeff', type: '图像' };
    } else {
      return { icon: '⚙️', color: '#6b7280', bgColor: '#f9fafb', type: '其他' };
    }
  }

  // 按类型分组方法
  function groupMethodsByType(methods: string[]) {
    const groups: Record<string, string[]> = {};
    methods.forEach(method => {
      const info = getMethodInfo(method);
      if (!groups[info.type]) {
        groups[info.type] = [];
      }
      groups[info.type].push(method);
    });
    return groups;
  }
</script>

<!-- 场景卡片容器 -->
<div class="scene-container">
  <!-- 场景标题卡片 -->
  <div class="scene-header-card">
    <div class="scene-title">
      <div class="scene-icon">🎯</div>
      <div class="scene-info">
        <h2>{sceneData.sceneName}</h2>
        <p class="scene-description">包含 {sceneData.buttons.length} 个交互按钮</p>
      </div>
    </div>
    <div class="scene-stats">
      <div class="stat-item">
        <span class="stat-number">{sceneData.buttons.length}</span>
        <span class="stat-label">按钮</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">{sceneData.buttons.reduce((sum, btn) => sum + btn.triggerMethods.length, 0)}</span>
        <span class="stat-label">方法</span>
      </div>
    </div>
  </div>

  <!-- 按钮卡片网格 -->
  <div class="buttons-grid">
    {#each sceneData.buttons as button}
      <div class="button-card">
        <!-- 按钮标题 -->
        <div class="button-header">
          <div class="button-title">
            <span class="button-icon">🔘</span>
            <h3>{button.buttonName}</h3>
          </div>
          <div class="method-count-badge">
            {button.triggerMethods.length}
          </div>
        </div>

        <!-- 方法分组 -->
        <div class="methods-container">
          {#each Object.entries(groupMethodsByType(button.triggerMethods)) as [type, methods]}
            {@const typeInfo = getMethodInfo(methods[0])}
            <div class="method-group">
              <div class="method-type-header" style="background-color: {typeInfo.bgColor}; color: {typeInfo.color};">
                <span class="type-icon">{typeInfo.icon}</span>
                <span class="type-name">{type}</span>
                <span class="type-count">({methods.length})</span>
              </div>
              <div class="method-list">
                {#each methods as method}
                  {@const methodInfo = getMethodInfo(method)}
                  <div class="method-item" style="border-left-color: {methodInfo.color};">
                    <code>{method}</code>
                  </div>
                {/each}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .scene-container {
    max-width: 100%;
    margin: 0 auto;
  }

  /* 场景标题卡片 */
  .scene-header-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .scene-title {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .scene-icon {
    font-size: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 12px;
    backdrop-filter: blur(10px);
  }

  .scene-info h2 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .scene-description {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
  }

  .scene-stats {
    display: flex;
    gap: 16px;
  }

  .stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 12px 16px;
    backdrop-filter: blur(10px);
    min-width: 60px;
  }

  .stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
  }

  .stat-label {
    display: block;
    font-size: 12px;
    opacity: 0.8;
    margin-top: 4px;
  }

  /* 按钮卡片网格 */
  .buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
  }

  .button-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .button-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  }

  .button-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
  }

  /* 按钮标题 */
  .button-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .button-title {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .button-icon {
    font-size: 20px;
  }

  .button-title h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }

  .method-count-badge {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
  }

  /* 方法分组 */
  .methods-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .method-group {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #f3f4f6;
  }

  .method-type-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .type-icon {
    font-size: 16px;
  }

  .type-name {
    flex: 1;
  }

  .type-count {
    font-size: 12px;
    opacity: 0.8;
  }

  .method-list {
    background: #fafafa;
    padding: 8px;
  }

  .method-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-left: 3px solid #3b82f6;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 6px;
    transition: all 0.2s ease;
  }

  .method-item:last-child {
    margin-bottom: 0;
  }

  .method-item:hover {
    background: #f8fafc;
    border-color: #3b82f6;
    transform: translateX(2px);
  }

  .method-item code {
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    font-size: 13px;
    color: #374151;
    background: none;
    padding: 0;
    font-weight: 500;
  }
</style>
