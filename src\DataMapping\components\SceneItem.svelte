<script lang="ts">
  import type { SceneDataFlow } from '../types';

  export let sceneData: SceneDataFlow;

  // 获取方法的说明
  function getMethodDescription(method: string): string {
    if (method.includes('DataManager.setupNewGame')) {
      return '初始化新游戏数据';
    } else if (method.includes('DataManager.saveGame')) {
      return '保存游戏数据';
    } else if (method.includes('DataManager.loadGame')) {
      return '加载游戏数据';
    } else if (method.includes('SceneManager.goto')) {
      return '切换到指定场景';
    } else if (method.includes('SceneManager.push')) {
      return '推入新场景到栈顶';
    } else if (method.includes('this.fadeOutAll')) {
      return '淡出所有音频和画面(组合)';
    } else if (method.includes('this.fadeOutAllAudio')) {
      return '只淡出音频';
    } else if (method.includes('this.startFadeOut')) {
      return '开始画面淡出';
    } else if (method.includes('this.checkGameover')) {
      return '检查游戏结束状态';
    } else if (method.includes('this.popScene')) {
      return '返回上一个场景';
    } else if (method.includes('$gameParty')) {
      return '操作队伍数据';
    } else if (method.includes('$gamePlayer')) {
      return '操作玩家数据';
    } else if (method.includes('AudioManager') || method.includes('SoundManager')) {
      return '播放音效或音乐';
    } else if (method.includes('ImageManager')) {
      return '加载图像资源';
    } else {
      return '执行游戏逻辑';
    }
  }
</script>

<!-- 场景容器 -->
<div class="scene-container">
  <!-- 场景标题 -->
  <div class="scene-header">
    <h2>{sceneData.sceneName}</h2>
    <div class="scene-stats">
      <span class="scene-count">{sceneData.buttons.length} 个UI元素</span>
      {#if sceneData.dataResources && sceneData.dataResources.length > 0}
        <span class="data-count">{sceneData.dataResources.length} 个数据资源</span>
      {/if}
    </div>
  </div>

  <!-- 数据资源部分 -->
  {#if sceneData.dataResources && sceneData.dataResources.length > 0}
    <div class="data-resources-section">
      <h3>📊 场景数据资源</h3>
      <div class="data-resources-grid">
        {#each sceneData.dataResources as resource, index}
          <div class="data-resource-item">
            <span class="resource-number">{index + 1}.</span>
            <code class="resource-path">{resource.dataPath}</code>
            <span class="resource-description">{resource.description}</span>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- UI卡片网格 -->
  <div class="ui-grid">
    {#each sceneData.buttons as button}
      <div class="ui-card">
        <!-- UI名称 -->
        <div class="ui-header">
          <h3>{button.buttonName}</h3>
        </div>

        <!-- 数据列表 -->
        <div class="data-list">
          {#each button.triggerMethods as method, index}
            <div class="data-item">
              <span class="data-number">{index + 1}.</span>
              <code class="data-method">{method}</code>
              <span class="data-description">{getMethodDescription(method)}</span>
            </div>
          {/each}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .scene-container {
    max-width: 100%;
    margin: 0 auto;
  }

  /* 场景标题 */
  .scene-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--theme-border);
  }

  .scene-header h2 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--theme-text);
  }

  .scene-stats {
    display: flex;
    gap: var(--spacing-2);
  }

  .scene-count, .data-count {
    background: var(--theme-primary-light);
    color: var(--theme-primary-dark);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
  }

  .data-count {
    background: var(--theme-accent-light);
    color: var(--theme-accent-dark);
  }

  /* 数据资源部分 */
  .data-resources-section {
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-3);
    background: var(--theme-surface-elevated);
    border-radius: var(--border-radius);
    border: 1px solid var(--theme-border);
  }

  .data-resources-section h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text);
  }

  .data-resources-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .data-resource-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    background: var(--theme-surface);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--theme-border);
  }

  .resource-number {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-weight: 500;
    min-width: 20px;
  }

  .resource-path {
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    color: var(--theme-success);
    background: var(--theme-surface-elevated);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--theme-border);
    font-weight: 500;
    flex: 1;
  }

  .resource-description {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-style: italic;
    flex-shrink: 0;
    max-width: 200px;
  }

  /* UI卡片网格 */
  .ui-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-4);
  }

  .ui-card {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
  }

  .ui-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--theme-primary);
  }

  /* UI标题 */
  .ui-header {
    background: var(--theme-surface-elevated);
    padding: var(--spacing-3);
    text-align: center;
  }

  .ui-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text);
  }

  /* 数据列表 */
  .data-list {
    padding: var(--spacing-3);
  }

  .data-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-2);
    padding: var(--spacing-2);
    background: var(--theme-surface-elevated);
  }

  .data-item:last-child {
    margin-bottom: 0;
  }

  .data-number {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-weight: 500;
    min-width: 20px;
  }

  .data-method {
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    color: var(--theme-accent);
    background: var(--theme-surface);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    flex: 1;
  }

  .data-description {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    font-style: italic;
    flex-shrink: 0;
    max-width: 300px;
  }
</style>
