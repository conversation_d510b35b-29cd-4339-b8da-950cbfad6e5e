/**
 * 抽象基类 - 所有对象模型的基础
 * 提供公共属性、方法和代码生成模板
 * 子类只需要实现特定的抽象方法
 */

// 不在基类中导入子类，避免循环依赖
import { historyManager } from '../historyManager';

export abstract class BaseObjectModel{

    // 静态注册容器 - 存储所有模型类
    private static modelRegistry = new Map<string, new (child: any) => BaseObjectModel>();

    /**
     * 注册模型类到容器中
     * @param key 注册键（类名或组件类型）
     * @param modelClass 模型类构造函数
     */
    static registerModel(key: string, modelClass: new (child: any) => BaseObjectModel): void {
        this.modelRegistry.set(key, modelClass);
        console.log(`🏭 BaseObjectModel: 注册模型类 ${key} -> ${modelClass.name}`);
    }

    /**
     * 获取已注册的模型类
     * @param key 注册键
     * @returns 模型类构造函数或undefined
     */
    static getRegisteredModel(key: string): (new (child: any) => BaseObjectModel) | undefined {
        return this.modelRegistry.get(key);
    }

    /**
     * 获取所有已注册的模型类型
     */
    static getRegisteredTypes(): string[] {
        return Array.from(this.modelRegistry.keys());
    }


    // 先定义响应式属性
    className: string = '';
    name = $state('');
    // 位置和变换
    x = $state(0);
    y = $state(0);
    scaleX = $state(1);
    scaleY = $state(1);
    skewX = $state(0);
    skewY = $state(0);
    rotation = $state(0);
    width = $state(0);
    height = $state(0);
    // 显示属性
    alpha = $state(1);
    visible = $state(true);
    // 锚点和轴心
    anchorX = $state(0);
    anchorY = $state(0);
    pivotX = $state(0);
    pivotY = $state(0);
    zIndex = $state(0);

    // 子对象管理
    children = $state<BaseObjectModel[]>([]);
    // 原始对象引用（子类可以重写）
    protected _originalObject: any = null;
    // 模型层面的父级引用
    protected _parentModel: BaseObjectModel | null = null;

    // 🎯 历史记录相关：待处理的目标索引
    private _pendingTargetIndex: number | null = null;

    /**
     * 获取父对象（返回保存的模型父级）
     */
    get parent(): BaseObjectModel | null {
        return this._parentModel;
    }

    /**
     * 设置父对象（保存模型层面的父级引用）
     */
    set parent(value: BaseObjectModel | null) {
        this._parentModel = value;
        console.log(`🔄 BaseObjectModel: 设置parent属性 ${this.className} → ${value?.className || 'null'}`);
    }

//     默认情况（generateChildrenCode = true）：
// child.generateCreationCode() 会调用 generateChildrenCreation()
// 子对象在这里被生成：obj_UIButton_5_child0
// pluginGenerator.ts 不再重复处理
// 特殊情况（generateChildrenCode = false）：
// 某些组件可能需要特殊的子对象处理逻辑
// 这时pluginGenerator.ts会接管子对象的生成适用于封装的UI组件（如slider）
    generateChildrenCode = true;

    // 🔧 更新回调函数
    public _onUpdate: (() => void) | null = null;



    // 基础清理函数，用于销毁 $effect.root
    protected cleanup: (() => void) | null = null;

    /**
     * 销毁方法（模板方法 - 子类不应重写）
     * 基类处理所有通用的销毁逻辑，子类通过 destroySpecific() 处理特有的清理
     */
    public destroy(): void {
        console.log(`🗑️ ${this.className}: 开始销毁对象`);

        // 1. 从父对象中移除自己
        this.removeFromParent();

        // 2. 清理子类特有的资源
        this.destroySpecific();

        // 3. 清理自身的响应式效果
        if (this.cleanup) {
            this.cleanup();
            this.cleanup = null;
            console.log(`🔧 ${this.className}: 已清理响应式效果`);
        }

        // 4. 销毁所有子对象的响应式效果
        this.children.forEach(child => {
            if (child.destroy) {
                child.destroy();
            }
        });

        // 5. 清理基础引用
        this._originalObject = null;
        this._onUpdate = null;
        this._parentModel = null;

        console.log(`✅ ${this.className}: 已完成销毁`);
    }

    /**
     * 带历史记录的删除方法
     * 用于支持撤销操作的删除
     */
    public destroyWithHistory(): void {
        console.log(`🎯 带历史记录删除: ${this.className}`);

        // 🎯 记录删除操作的历史 - 新方案：保存完整对象数据
        if (historyManager.isRecording()) {
            const parent = this.parent;
            const index = parent ? parent.children.indexOf(this) : -1;

            // 🔑 创建完整的对象数据快照（包括所有子对象）
            const objectSnapshot = this.createCompleteSnapshot();

            historyManager.startGroup('删除节点', `删除${this.className}`);

            // 记录删除操作的恢复信息
            historyManager.recordChange(parent || this, '_deleteRestore', null, {
                deletedObject: objectSnapshot,
                parentObject: parent,
                insertIndex: index,
                timestamp: Date.now()
            });

            console.log(`📝 记录删除历史: ${this.className} 从 ${parent?.className || 'root'}[${index}]`);

            historyManager.endGroup();
        }

        // 执行实际删除
        this.destroy();
    }

    /**
     * 从父对象中移除自己
     */
    private removeFromParent(): void {
        const parentObject = this.parent;
        if (parentObject) {
            const index = parentObject.children.indexOf(this);
            if (index !== -1) {
                // 从父对象的children数组中移除
                parentObject.children.splice(index, 1);
                console.log(`🔄 从父对象移除: ${this.className} 从 ${parentObject.className}`);

                // 从原始PIXI对象中移除
                const thisOriginal = this.getOriginalObject();
                if (thisOriginal && thisOriginal.parent && typeof thisOriginal.parent.removeChild === 'function') {
                    try {
                        // 🔑 注意：这里不设置重排序状态，因为这是真正的销毁操作
                        // 重排序状态只在 setParentAndIndex 中设置
                        thisOriginal.parent.removeChild(thisOriginal);
                        console.log(`🔄 从原始PIXI父对象移除: ${this.className}`);

                        // 🔧 安全检查：确保对象仍然有效再销毁
                        if (typeof thisOriginal.destroy === 'function' &&
                            !thisOriginal._isDestroyed &&
                            thisOriginal.transform) { // 确保transform仍然存在
                            thisOriginal._isDestroyed = true; // 标记为已销毁，避免重复调用
                            thisOriginal.destroy();
                            console.log(`🗑️ 已调用原始对象的 destroy: ${this.className}`);
                        } else if (!thisOriginal.transform) {
                            console.warn(`🚨 ${this.className}: 原始对象的transform已为null，跳过销毁`);
                        }
                    } catch (error) {
                        console.error(`🚨 ${this.className}: 移除原始对象时发生错误`, error);
                    }
                }
            }
        }
    }

    /**
     * 销毁特有资源（子类可以重写）
     * 子类在此方法中清理自己特有的资源，如事件监听器、定时器等
     */
    protected destroySpecific(): void {
        // 默认空实现，子类可选重写
    }

    constructor(obj: any){
        // 保存原始对象引用
        this._originalObject = obj;

        // 设置非响应式属性
        this.className = obj.constructor.name;

        // 设置响应式属性的初始值
        this.name = obj.name || '';
        this.x = obj.x || 0;
        this.y = obj.y || 0;
        this.scaleX = obj.scale?.x || 1;
        this.scaleY = obj.scale?.y || 1;
        this.skewX = obj.skew?.x || 0;
        this.skewY = obj.skew?.y || 0;
        this.rotation = obj.rotation || 0;
        this.width = obj.width || 0;
        this.height = obj.height || 0;
        this.alpha = obj.alpha !== undefined ? obj.alpha : 1;
        this.visible = obj.visible !== undefined ? obj.visible : true;
        this.anchorX = obj.anchor?.x || 0;
        this.anchorY = obj.anchor?.y || 0;
        this.pivotX = obj.pivot?.x || 0;
        this.pivotY = obj.pivot?.y || 0;
        this.zIndex = obj.zIndex || 0;

        // 初始化子对象（如果有的话）
        this.children = this.createChildrenModels(obj.children || []);

        // 设置基础响应式同步（子类可以重写）
        this.setupSync();
    }

    /**
     * 设置基础响应式同步（模板方法 - 子类不应重写）
     * 基类处理所有通用属性，子类通过 setupSpecificSync() 处理特有属性
     */
    protected setupSync(): void {
        // 只有当原始对象存在时才设置同步
        if (!this._originalObject) return;

        // 使用 $effect.root 创建独立的响应式根
        this.cleanup = $effect.root(() => {
            $effect(() => {
                if (this._originalObject) {
                    // 1. 同步基础属性（基类处理）
                    this.syncBasicProperties();

                    // 2. 同步特有属性（子类实现）
                    this.setupSpecificSync();

                    // 3. 调用更新回调
                    if (this._onUpdate) {
                        this._onUpdate();
                    }

                    console.log(`🔧 ${this.className}: 所有属性已同步`);
                }
            });

            // 返回清理函数
            return () => {
                console.log(`🔧 ${this.className}: 清理响应式效果`);
            };
        });
    }

    /**
     * 同步基础属性（基类实现 - 处理所有通用属性）
     */
    private syncBasicProperties(): void {
        // 🔑 名称属性
        this._originalObject.name = this.name;

        // 位置和尺寸属性
        this._originalObject.x = this.x;
        this._originalObject.y = this.y;
        this._originalObject.width = this.width;
        this._originalObject.height = this.height;

        // 显示属性
        this._originalObject.alpha = this.alpha;
        this._originalObject.visible = this.visible;
        this._originalObject.rotation = this.rotation;
        this._originalObject.zIndex = this.zIndex;

        // 同步缩放属性 - 使用 scale 对象
        if (this._originalObject.scale) {
            this._originalObject.scale.x = this.scaleX;
            this._originalObject.scale.y = this.scaleY;
        }

        // 同步倾斜属性 - 使用 skew 对象
        if (this._originalObject.skew) {
            this._originalObject.skew.x = this.skewX;
            this._originalObject.skew.y = this.skewY;
        }

        // 同步锚点属性 - 使用 anchor 对象
        if (this._originalObject.anchor) {
            this._originalObject.anchor.x = this.anchorX;
            this._originalObject.anchor.y = this.anchorY;
        }

        // 同步轴心属性 - 使用 pivot 对象
        if (this._originalObject.pivot) {
            this._originalObject.pivot.x = this.pivotX;
            this._originalObject.pivot.y = this.pivotY;
        }
    }

    /**
     * 设置特有属性同步（子类可以重写）
     * 子类在此方法中只处理自己特有的属性同步
     */
    protected setupSpecificSync(): void {
        // 默认空实现，子类可选重写
        // 子类只需要在这里处理自己特有的属性
    }

    /**
     * 创建子对象的响应式模型
     * 子类可以重写此方法来创建特定类型的模型
     */
    protected createChildrenModels(children: any[]): BaseObjectModel[] {
        return children.map((child: any) => {
            const childModel = this.createChildModel(child);
            // 设置父级引用
            childModel.parent = this;
            return childModel;
        });
    }

    /**
     * 创建单个子对象模型（基类统一实现）
     * 根据对象类型自动创建相应的模型实例
     * @param child 子对象
     * @returns 对应的模型实例
     */
    protected createChildModel(child: any): BaseObjectModel {
        const className = child.constructor.name;

        // 🔧 调试信息：详细输出子对象信息
        console.log(`🔧 ${this.className}: 分析子对象`, {
            className: className,
            isUIComponent: child.isUIComponent,
            uiComponentType: child.uiComponentType,
            registeredTypes: BaseObjectModel.getRegisteredTypes()
        });

        // 优先检查UI组件类型
        if (child.isUIComponent && child.uiComponentType) {
            const uiType = child.uiComponentType;
            const ModelClass = BaseObjectModel.getRegisteredModel(uiType);
            if (ModelClass) {
                console.log(`🔧 ${this.className}: 创建 ${uiType}Model (UI组件)`, className);
                return new ModelClass(child) as BaseObjectModel;
            }
        }

        // 检查精确类名匹配
        const exactModelClass = BaseObjectModel.getRegisteredModel(className);
        if (exactModelClass) {
            console.log(`🔧 ${this.className}: 创建模型 (精确匹配)`, className);
            return new exactModelClass(child) as BaseObjectModel;
        }

        // 模糊匹配 - 检查类名包含关系
        for (const registeredKey of BaseObjectModel.getRegisteredTypes()) {
            if (className.includes(registeredKey) || registeredKey.includes(className)) {
                const ModelClass = BaseObjectModel.getRegisteredModel(registeredKey);
                if (ModelClass) {
                    console.log(`🔧 ${this.className}: 创建模型 (模糊匹配)`, className, '→', registeredKey);
                    return new ModelClass(child) as BaseObjectModel;
                }
            }
        }

        // 默认回退到Sprite
        const SpriteModelClass = BaseObjectModel.getRegisteredModel('Sprite');
        if (SpriteModelClass) {
            console.warn(`🔧 ${this.className}: 使用默认SpriteModel`, className);
            return new SpriteModelClass(child) as BaseObjectModel;
        }

        // 如果连SpriteModel都没有注册，抛出错误
        console.error(`🔧 ${this.className}: 无法为 ${className} 创建模型，请确保相关模型类已注册`);
        console.error(`🔧 已注册的模型类型:`, BaseObjectModel.getRegisteredTypes());
        throw new Error(`无法为 ${className} 创建模型，请确保相关模型类已注册`);
    }

    /**
     * 添加子对象
     */
    public addChild(child: any): void {
        const childModel = this.createChildModel(child);

        // 先尝试同步到原始对象，如果失败则不修改模型
        if (this._originalObject && typeof this._originalObject.addChild === 'function') {
            try {
                this._originalObject.addChild(child);
            } catch (error) {
                console.error(`🔧 ${this.className}: 添加子对象失败`, error);
                // 如果原始对象添加失败，抛出错误，不修改模型
                throw error;
            }
        }

        // 只有原始对象添加成功后，才修改模型
        // 设置子对象的父级引用
        childModel.parent = this;
        this.children.push(childModel);

        console.log(`🔧 ${this.className}: 添加子对象成功`, child.constructor.name);
    }

    /**
     * 移除子对象
     */
    public removeChild(index: number): void {
        if (index >= 0 && index < this.children.length) {
            const childModel = this.children[index];

            // 先获取对应的原始子对象（在修改数组之前）
            const originalChild = this._originalObject?.children?.[index];

            // 先尝试从原始对象中移除
            if (this._originalObject && typeof this._originalObject.removeChild === 'function' && originalChild) {
                try {
                    // 🔧 安全检查：确保子对象仍然有效
                    if (originalChild.transform) {
                        this._originalObject.removeChild(originalChild);
                        console.log(`🔄 ${this.className}: 成功移除子对象`);
                    } else {
                        console.warn(`🚨 ${this.className}: 子对象的transform已为null，跳过移除`);
                    }
                } catch (error) {
                    console.error(`🔧 ${this.className}: 移除子对象失败`, error);
                    // 如果原始对象移除失败，抛出错误，不修改模型
                    throw error;
                }
            }

            // 只有原始对象移除成功后，才修改模型
            // 清除子对象的父级引用
            childModel.parent = null;

            // 销毁子对象的响应式效果
            if (childModel.destroy) {
                childModel.destroy();
            }

            // 从模型数组中移除
            this.children.splice(index, 1);

            console.log(`🔧 ${this.className}: 移除子对象成功`, index);
        }
    }

    /**
     * 根据对象移除子对象
     */
    public removeChildByObject(childModel: BaseObjectModel): void {
        const index = this.children.indexOf(childModel);
        if (index !== -1) {
            this.removeChild(index);
        }
    }

    /**
     * 清空所有子对象
     */
    public clearChildren(): void {
        // 先尝试清空原始对象的子对象
        if (this._originalObject && this._originalObject.children) {
            try {
                if (typeof this._originalObject.removeChildren === 'function') {
                    this._originalObject.removeChildren();
                } else if (Array.isArray(this._originalObject.children)) {
                    this._originalObject.children.length = 0;
                }
            } catch (error) {
                console.error(`🔧 ${this.className}: 清空原始对象子对象失败`, error);
                throw error;
            }
        }

        // 只有原始对象清空成功后，才清空模型
        // 销毁所有子对象的响应式效果
        this.children.forEach(child => {
            if (child.destroy) {
                child.destroy();
            }
        });

        this.children.length = 0;

        console.log(`🔧 ${this.className}: 清空所有子对象成功`);
    }

    /**
     * 获取原始对象
     */
    public getOriginalObject(): any {
        return this._originalObject;
    }

    /**
     * 根据索引获取子对象
     */
    public getChildAt(index: number): BaseObjectModel | null {
        return this.children[index] || null;
    }

    /**
     * 获取子对象数量
     */
    public getChildCount(): number {
        return this.children.length;
    }

    /**
     * 查找子对象
     */
    public findChild(predicate: (child: BaseObjectModel) => boolean): BaseObjectModel | null {
        return this.children.find(predicate) || null;
    }

    /**
     * 根据名称查找子对象
     */
    public findChildByName(name: string): BaseObjectModel | null {
        return this.findChild(child => child.name === name);
    }

    /**
     * 根据类名查找子对象
     */
    public findChildByClassName(className: string): BaseObjectModel | null {
        return this.findChild(child => child.className === className);
    }

    /**
     * 获取所有指定类型的子对象
     */
    public getChildrenByClassName(className: string): BaseObjectModel[] {
        return this.children.filter(child => child.className === className);
    }

    /**
     * 🔑 递归标记对象及其所有子对象为重排序状态
     */
    private markAsBeingReordered(originalObject: any): void {
        if (originalObject && typeof originalObject.markAsBeingReordered === 'function') {
            originalObject.markAsBeingReordered();
            console.log(`🔄 标记重排序状态: ${this.className}`);
        }

        // 🔑 递归标记所有子对象
        this.children.forEach(child => {
            const childOriginal = child.getOriginalObject();
            if (childOriginal && typeof childOriginal.markAsBeingReordered === 'function') {
                childOriginal.markAsBeingReordered();
                console.log(`🔄 标记子对象重排序状态: ${child.className}`);
            }
        });
    }

    /**
     * 🔑 递归取消对象及其所有子对象的重排序状态
     */
    private unmarkAsBeingReordered(originalObject: any): void {
        if (originalObject && typeof originalObject.unmarkAsBeingReordered === 'function') {
            originalObject.unmarkAsBeingReordered();
            console.log(`✅ 取消重排序状态: ${this.className}`);
        }

        // 🔑 递归取消所有子对象的重排序状态
        this.children.forEach(child => {
            const childOriginal = child.getOriginalObject();
            if (childOriginal && typeof childOriginal.unmarkAsBeingReordered === 'function') {
                childOriginal.unmarkAsBeingReordered();
                console.log(`✅ 取消子对象重排序状态: ${child.className}`);
            }
        });
    }

    /**
     * 设置父对象和索引位置（拖拽重排专用）
     * @param newParent 新的父对象
     * @param index 索引位置（可选，默认添加到末尾）
     */
    public setParentAndIndex(newParent: BaseObjectModel, index?: number): void {
        console.log(`🔄 设置父对象和索引: ${this.className} → ${newParent.className}[${index ?? 'end'}]`);

        // 🎯 记录历史 - 获取操作前的状态
        const currentParent = this.parent;
        const currentIndex = currentParent ? currentParent.children.indexOf(this) : -1;
        const finalIndex = index ?? newParent.children.length;

        // 🎯 开始历史记录操作组
        if (historyManager.isRecording()) {
            historyManager.startGroup('移动节点', `移动${this.className}到${newParent.className}`);

            // 记录父节点变化
            historyManager.recordChange(this, '_treeParent', currentParent, newParent);

            // 记录索引变化
            historyManager.recordChange(this, '_treeIndex', currentIndex, finalIndex);

            console.log(`📝 记录树操作历史: ${this.className} 从 ${currentParent?.className || 'root'}[${currentIndex}] 到 ${newParent.className}[${finalIndex}]`);
        }

        const thisOriginal = this.getOriginalObject();

        // 🔑 标记为重排序状态，防止 UIComponent 触发销毁逻辑
        this.markAsBeingReordered(thisOriginal);

        // 1. 先从当前父级移除（如果有的话）
        if (currentParent) {
            const currentIndex = currentParent.children.indexOf(this);
            if (currentIndex !== -1) {
                // 从当前父级的children数组中移除
                currentParent.children.splice(currentIndex, 1);
                console.log(`🔄 从当前父级移除: ${this.className} 从 ${currentParent.className}`);

                // 从原始PIXI对象中移除
                if (thisOriginal && thisOriginal.parent && typeof thisOriginal.parent.removeChild === 'function') {
                    thisOriginal.parent.removeChild(thisOriginal);
                    console.log(`🔄 从原始PIXI父级移除: ${this.className}`);
                }
            }
        }

        // 2. 添加到新父对象的模型数组
        if (index !== undefined && index >= 0 && index <= newParent.children.length) {
            // 插入到指定位置
            newParent.children.splice(index, 0, this);
        } else {
            // 添加到末尾
            newParent.children.push(this);
        }

        // 3. 设置新的父级引用
        this.parent = newParent;

        // 4. 同步到原始对象
        const newParentOriginal = newParent.getOriginalObject();
        if (newParentOriginal && thisOriginal) {
            if (typeof newParentOriginal.addChild === 'function') {
                newParentOriginal.addChild(thisOriginal);
                console.log(`🔄 添加到原始对象: ${this.className} → ${newParent.className}`);

                // 如果是插入到指定位置，需要重新排列顺序
                if (index !== undefined) {
                    this.syncChildrenOrder(newParent);
                }
            }
        }

        console.log(`✅ 完成重排: ${this.className} 现在是 ${newParent.className} 的子对象`);

        // 🔑 取消重排序状态标记
        this.unmarkAsBeingReordered(thisOriginal);

        // 🎯 结束历史记录操作组
        if (historyManager.isRecording()) {
            historyManager.endGroup();
        }
    }

    /**
     * 同步子对象顺序到原始PIXI对象
     */
    private syncChildrenOrder(parent: BaseObjectModel): void {
        const parentOriginal = parent.getOriginalObject();

        if (parentOriginal && Array.isArray(parentOriginal.children)) {
            // 重新排列原始对象的children数组
            const newOriginalChildren: any[] = [];

            for (const childModel of parent.children) {
                const childOriginal = childModel.getOriginalObject();
                if (childOriginal) {
                    newOriginalChildren.push(childOriginal);
                }
            }

            // 替换原始对象的children数组
            parentOriginal.children.length = 0;
            parentOriginal.children.push(...newOriginalChildren);

            console.log(`🔄 同步子对象顺序: ${parent.className} (${newOriginalChildren.length} 个子对象)`);
        }
    }

    /**
     * 创建完整的对象快照（包括所有子对象）
     * 用于删除恢复操作
     */
    public createCompleteSnapshot(): any {
        console.log(`📸 创建对象快照: ${this.className}`);

        const snapshot = {
            // 基础信息
            className: this.className,
            name: this.name,

            // 位置和变换
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            scaleX: this.scaleX,
            scaleY: this.scaleY,
            rotation: this.rotation,
            alpha: this.alpha,
            visible: this.visible,
            anchorX: this.anchorX,
            anchorY: this.anchorY,
            pivotX: this.pivotX,
            pivotY: this.pivotY,
            skewX: this.skewX,
            skewY: this.skewY,
            zIndex: this.zIndex,

            // 🔑 保存原始对象的构造参数（如果有的话）
            originalConstructorArgs: this.getConstructorArgs(),

            // 🔑 递归保存所有子对象的快照
            children: this.children.map(child => child.createCompleteSnapshot()),

            // 时间戳
            snapshotTimestamp: Date.now()
        };

        console.log(`📸 快照创建完成: ${this.className}，包含 ${snapshot.children.length} 个子对象`);
        return snapshot;
    }

    /**
     * 获取构造函数参数（子类可以重写）
     * 用于重建对象时的参数
     */
    protected getConstructorArgs(): any {
        // 基类返回基础属性，子类可以重写添加特有属性
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha
        };
    }

    /**
     * 从快照恢复对象（静态方法）
     */
    public static restoreFromSnapshot(snapshot: any): BaseObjectModel | null {
        try {
            console.log(`🔄 从快照恢复对象: ${snapshot.className}`);

            // 1. 根据 className 创建对应的原始对象
            const restoredOriginal = BaseObjectModel.createOriginalObjectFromSnapshot(snapshot);
            if (!restoredOriginal) {
                console.error(`❌ 无法创建原始对象: ${snapshot.className}`);
                return null;
            }

            // 2. 创建对应的模型对象
            const restoredModel = BaseObjectModel.createModelFromOriginal(restoredOriginal);
            if (!restoredModel) {
                console.error(`❌ 无法创建模型对象: ${snapshot.className}`);
                return null;
            }

            // 3. 恢复基础属性
            restoredModel.name = snapshot.name || '';
            restoredModel.x = snapshot.x || 0;
            restoredModel.y = snapshot.y || 0;
            restoredModel.width = snapshot.width || 0;
            restoredModel.height = snapshot.height || 0;
            restoredModel.scaleX = snapshot.scaleX || 1;
            restoredModel.scaleY = snapshot.scaleY || 1;
            restoredModel.rotation = snapshot.rotation || 0;
            restoredModel.alpha = snapshot.alpha !== undefined ? snapshot.alpha : 1;
            restoredModel.visible = snapshot.visible !== undefined ? snapshot.visible : true;
            restoredModel.anchorX = snapshot.anchorX || 0;
            restoredModel.anchorY = snapshot.anchorY || 0;
            restoredModel.pivotX = snapshot.pivotX || 0;
            restoredModel.pivotY = snapshot.pivotY || 0;
            restoredModel.skewX = snapshot.skewX || 0;
            restoredModel.skewY = snapshot.skewY || 0;
            restoredModel.zIndex = snapshot.zIndex || 0;

            // 4. 递归恢复所有子对象
            if (snapshot.children && Array.isArray(snapshot.children)) {
                const restoredChildren: BaseObjectModel[] = [];
                for (const childSnapshot of snapshot.children) {
                    const restoredChild = BaseObjectModel.restoreFromSnapshot(childSnapshot);
                    if (restoredChild) {
                        restoredChildren.push(restoredChild);
                        restoredChild.parent = restoredModel;

                        // 添加到原始对象
                        const childOriginal = restoredChild.getOriginalObject();
                        if (childOriginal && restoredOriginal.addChild) {
                            restoredOriginal.addChild(childOriginal);
                        }
                    }
                }
                restoredModel.children = restoredChildren;
            }

            console.log(`✅ 对象恢复完成: ${snapshot.className}，包含 ${restoredModel.children.length} 个子对象`);
            return restoredModel;

        } catch (error) {
            console.error(`❌ 从快照恢复对象失败:`, error);
            return null;
        }
    }

    /**
     * 从快照创建原始对象（静态方法）
     */
    private static createOriginalObjectFromSnapshot(snapshot: any): any {
        try {
            const className = snapshot.className;
            const args = snapshot.originalConstructorArgs || {};

            console.log(`🏗️ 创建原始对象: ${className}`, args);

            // 根据类名创建对应的原始对象
            if (typeof window !== 'undefined') {
                // UI 组件
                if (className === 'UIImage' && (window as any).UIImage) {
                    return new (window as any).UIImage(args);
                }
                if (className === 'UILabel' && (window as any).UILabel) {
                    return new (window as any).UILabel(args);
                }
                if (className === 'UIButton' && (window as any).UIButton) {
                    return new (window as any).UIButton(args);
                }
                if (className === 'UISlider' && (window as any).UISlider) {
                    return new (window as any).UISlider(args);
                }
                if (className === 'UIList' && (window as any).UIList) {
                    return new (window as any).UIList(args);
                }
                if (className === 'UIAtlas' && (window as any).UIAtlas) {
                    return new (window as any).UIAtlas(args);
                }

                // PIXI 基础对象
                if (className === 'Sprite' && (window as any).PIXI?.Sprite) {
                    return new (window as any).PIXI.Sprite();
                }
                if (className === 'Container' && (window as any).PIXI?.Container) {
                    return new (window as any).PIXI.Container();
                }

                // RPG Maker MZ 对象
                if ((window as any)[className]) {
                    return new (window as any)[className](args);
                }
            }

            console.error(`❌ 无法创建原始对象，未知类型: ${className}`);
            return null;

        } catch (error) {
            console.error(`❌ 创建原始对象失败:`, error);
            return null;
        }
    }

    /**
     * 从原始对象创建模型对象（静态方法）
     */
    private static createModelFromOriginal(originalObject: any): BaseObjectModel | null {
        try {
            const className = originalObject.constructor.name;

            console.log(`🎭 创建模型对象: ${className}`);

            // 查找注册的模型类
            const ModelClass = BaseObjectModel.modelRegistry.get(className);
            if (ModelClass) {
                return new ModelClass(originalObject);
            }

            // 如果没有找到特定的模型类，使用基础模型类
            console.warn(`⚠️ 未找到 ${className} 的模型类，使用基础模型`);

            // 这里需要一个通用的模型类，暂时返回 null
            // 在实际实现中，可能需要一个 GenericModel 类
            return null;

        } catch (error) {
            console.error(`❌ 创建模型对象失败:`, error);
            return null;
        }
    }

    /**
     * 克隆当前对象（抽象方法，子类必须实现）
     */
    abstract clone(): BaseObjectModel;

    /**
     * 带历史记录的复制方法
     * 复制对象并添加到指定父节点，支持撤销操作
     * @param targetParent 目标父节点（可选，默认为当前父节点）
     * @param targetIndex 目标索引（可选，默认添加到末尾）
     * @returns 复制的新对象
     */
    public cloneWithHistory(targetParent?: BaseObjectModel | null, targetIndex?: number): BaseObjectModel | null {
        try {
            console.log(`🎯 带历史记录复制: ${this.className}`);

            // 1. 执行克隆
            const clonedObject = this.clone();

            // 2. 确定目标父节点和索引
            const finalParent = targetParent !== undefined ? targetParent : this.parent;
            const finalIndex = targetIndex !== undefined ? targetIndex :
                (finalParent ? finalParent.children.length : 0);

            // 🎯 记录复制操作的历史
            if (historyManager.isRecording()) {
                historyManager.startGroup('复制节点', `复制${this.className}`);

                // 🎯 修复：调整记录顺序，确保撤销时正确移除（撤销时会反向执行）
                historyManager.recordChange(clonedObject, '_treeIndex', -1, finalIndex);    // 最后撤销：索引
                historyManager.recordChange(clonedObject, '_treeParent', null, finalParent); // 然后撤销：父节点
                historyManager.recordChange(clonedObject, '_treeCreated', false, true);     // 首先撤销：创建状态

                console.log(`📝 记录复制历史: 创建${clonedObject.className} 到 ${finalParent?.className || 'root'}[${finalIndex}]`);

                historyManager.endGroup();
            }

            // 3. 添加到目标父节点
            if (finalParent) {
                if (finalIndex >= 0 && finalIndex <= finalParent.children.length) {
                    finalParent.children.splice(finalIndex, 0, clonedObject);
                } else {
                    finalParent.children.push(clonedObject);
                }
                clonedObject.parent = finalParent;

                // 同步到原始对象
                const parentOriginal = finalParent.getOriginalObject();
                const clonedOriginal = clonedObject.getOriginalObject();
                if (parentOriginal && clonedOriginal && typeof parentOriginal.addChild === 'function') {
                    parentOriginal.addChild(clonedOriginal);
                    console.log(`🎯 同步复制对象到原始对象: ${clonedObject.className} → ${finalParent.className}`);
                }
            }

            console.log(`✅ 复制完成: ${this.className} → ${clonedObject.className}`);
            return clonedObject;

        } catch (error) {
            console.error(`❌ 复制失败: ${this.className}`, error);
            return null;
        }
    }



    /**
     * 生成创建此对象的代码（模板方法）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码（子类实现）
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置（基类实现）
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（子类实现）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成（基类实现）
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成对象创建代码（抽象方法 - 子类必须实现）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected abstract generateObjectCreation(varName: string, indent: string): string;

    /**
     * 生成基础属性设置代码（基类实现）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 基础属性设置代码
     */
    protected generateBasicProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 名称属性
        if (this.name && this.name.trim() !== '') {
            codes.push(`${indent}${varName}.name = ${JSON.stringify(this.name)};`);
        }

        // 位置属性
        if (this.x !== 0 || this.y !== 0) {
            codes.push(`${indent}${varName}.x = ${this.x};`);
            codes.push(`${indent}${varName}.y = ${this.y};`);
        }

        // 尺寸属性
        if (this.width !== 0 || this.height !== 0) {
            codes.push(`${indent}${varName}.width = ${this.width};`);
            codes.push(`${indent}${varName}.height = ${this.height};`);
        }

        // 缩放属性
        if (this.scaleX !== 1 || this.scaleY !== 1) {
            codes.push(`${indent}${varName}.scale.x = ${this.scaleX};`);
            codes.push(`${indent}${varName}.scale.y = ${this.scaleY};`);
        }

        // 显示属性
        if (this.alpha !== 1) {
            codes.push(`${indent}${varName}.alpha = ${this.alpha};`);
        }

        if (!this.visible) {
            codes.push(`${indent}${varName}.visible = ${this.visible};`);
        }

        // 旋转属性
        if (this.rotation !== 0) {
            codes.push(`${indent}${varName}.rotation = ${this.rotation};`);
        }

        // 锚点属性
        if (this.anchorX !== 0 || this.anchorY !== 0) {
            codes.push(`${indent}${varName}.anchor.x = ${this.anchorX};`);
            codes.push(`${indent}${varName}.anchor.y = ${this.anchorY};`);
        }

        // 轴心属性
        if (this.pivotX !== 0 || this.pivotY !== 0) {
            codes.push(`${indent}${varName}.pivot.x = ${this.pivotX};`);
            codes.push(`${indent}${varName}.pivot.y = ${this.pivotY};`);
        }

        // 倾斜属性
        if (this.skewX !== 0 || this.skewY !== 0) {
            codes.push(`${indent}${varName}.skew.x = ${this.skewX};`);
            codes.push(`${indent}${varName}.skew.y = ${this.skewY};`);
        }

        // Z轴层级
        if (this.zIndex !== 0) {
            codes.push(`${indent}${varName}.zIndex = ${this.zIndex};`);
        }

        return codes.join('\n');
    }

    /**
     * 生成特定属性设置代码（子类可以重写）
     * @param _varName 变量名
     * @param _indent 缩进字符串
     * @returns 特定属性设置代码
     */
    protected generateSpecificProperties(_varName: string, _indent: string): string {
        // 默认空实现，子类可选重写
        return '';
    }

    /**
     * 生成子对象创建代码（基类实现）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 子对象创建代码
     */
    protected generateChildrenCreation(varName: string, indent: string): string {
        if (this.children.length === 0) {
            return '';
        }

        // 🔑 过滤掉 PIXI.Graphics 对象
        const validChildren = this.children.filter((child) => {
            const className = child.className || child.constructor?.name || '';
            const isGraphics = className === 'Graphics' ||
                              className === 'PIXI.Graphics' ||
                              className.includes('Graphics');

            if (isGraphics) {
                console.log(`🎨 BaseObjectModel: 代码生成时过滤掉 Graphics 对象 (${className})`);
                return false;
            }

            return true;
        });

        if (validChildren.length === 0) {
            return '';
        }

        const codes: string[] = [];
        codes.push(`${indent}// 添加子对象`);

        validChildren.forEach((child, index) => {
            const childVarName = `${varName}_child${index}`;
            codes.push(child.generateCreationCode(childVarName, indent));
            codes.push(`${indent}${varName}.addChild(${childVarName});`);
        });

        return codes.join('\n');
    }

    // ==================== 🎯 历史记录支持的虚拟属性 ====================

    /**
     * 虚拟属性：树父节点（用于历史记录）
     * 当历史记录系统恢复此属性时，会触发实际的父子关系变更
     */
    get _treeParent(): BaseObjectModel | null {
        return this.parent;
    }

    set _treeParent(newParent: BaseObjectModel | null) {
        console.log(`🎯 历史记录恢复: 设置 ${this.className} 的父节点为 ${newParent?.className || 'null'}`);
        this.executeParentChange(newParent);
    }

    /**
     * 虚拟属性：树索引（用于历史记录）
     * 当历史记录系统恢复此属性时，会触发实际的索引调整
     */
    get _treeIndex(): number {
        const parent = this.parent;
        return parent ? parent.children.indexOf(this) : -1;
    }

    set _treeIndex(newIndex: number) {
        console.log(`🎯 历史记录恢复: 设置 ${this.className} 的索引为 ${newIndex}`);
        // 🎯 修复：保存目标索引，在父节点变更完成后再执行
        this._pendingTargetIndex = newIndex;
        this.executeIndexChange(newIndex);
    }

    /**
     * 虚拟属性：删除状态（用于历史记录）
     */
    get _treeDeleted(): boolean {
        return false; // 存在的对象总是返回 false
    }

    set _treeDeleted(deleted: boolean) {
        console.log(`🎯 历史记录恢复: 设置 ${this.className} 删除状态为 ${deleted}`);
        // 🎯 修复：删除状态的变更主要用于标记，实际的删除/恢复由父子关系变更处理
        if (deleted) {
            console.log(`🎯 标记为删除状态: ${this.className}`);
            // 不执行实际删除，因为这是撤销过程中的状态恢复
        } else {
            console.log(`🎯 标记为恢复状态: ${this.className}`);
            // 恢复状态，实际的恢复由 _treeParent 和 _treeIndex 处理
        }
    }

    /**
     * 虚拟属性：创建状态（用于历史记录）
     */
    get _treeCreated(): boolean {
        return true; // 存在的对象总是返回 true
    }

    set _treeCreated(created: boolean) {
        console.log(`🎯 历史记录恢复: 设置 ${this.className} 创建状态为 ${created}`);
        if (!created) {
            // 🎯 撤销复制操作：移除复制的对象
            this.executeRemoveCreated();
        } else {
            console.log(`🎯 标记为创建状态: ${this.className}`);
            // 创建状态，通常不需要特殊处理
        }
    }

    // ==================== 🎯 历史记录执行方法 ====================

    /**
     * 执行父节点变更（历史记录恢复时调用）
     */
    private executeParentChange(newParent: BaseObjectModel | null): void {
        console.log(`🎯 执行父节点变更: ${this.className} 目标父节点 ${newParent?.className || 'null'}`);

        const currentParent = this.parent;

        // 如果父节点没有变化，跳过
        if (currentParent === newParent) {
            console.log(`🎯 父节点无需变更: ${this.className} 已在正确父节点`);
            return;
        }

        // 从当前父节点移除
        if (currentParent) {
            const currentIndex = currentParent.children.indexOf(this);
            if (currentIndex !== -1) {
                currentParent.children.splice(currentIndex, 1);
                console.log(`🎯 从父节点移除: ${this.className} 从 ${currentParent.className}[${currentIndex}]`);
            }
        }

        // 设置新的父节点引用
        this.parent = newParent;

        // 添加到新父节点
        if (newParent) {
            // 🎯 修复：恢复删除的节点时，先添加到末尾
            // 具体的索引位置会在 executeIndexChange 中正确设置
            if (!newParent.children.includes(this)) {
                newParent.children.push(this);
                console.log(`🎯 添加到父节点: ${this.className} 到 ${newParent.className} (临时位置: ${newParent.children.length - 1})`);
            } else {
                console.log(`🎯 节点已在父节点中: ${this.className} 在 ${newParent.className}`);
            }
        } else {
            console.log(`🎯 设置为根节点: ${this.className}`);
        }

        // 同步到原始对象
        this.syncToOriginalObject();

        // 🎯 修复：如果有待处理的目标索引，现在处理它
        if (this._pendingTargetIndex !== null && newParent) {
            const targetIndex = this._pendingTargetIndex;
            this._pendingTargetIndex = null; // 清除待处理状态

            console.log(`🎯 处理待处理的索引: ${this.className} 目标索引 ${targetIndex}`);

            // 现在父节点已经正确设置，可以安全地调整索引
            const currentIndex = newParent.children.indexOf(this);
            if (currentIndex !== -1 && currentIndex !== targetIndex) {
                // 移动到正确位置
                newParent.children.splice(currentIndex, 1); // 移除
                const safeIndex = Math.min(targetIndex, newParent.children.length);
                newParent.children.splice(safeIndex, 0, this); // 插入到目标位置

                console.log(`🎯 索引调整完成: ${this.className} 从 ${currentIndex} 移动到 ${safeIndex}`);

                // 再次同步到原始对象
                this.syncChildrenOrder(newParent);
            }
        }
    }

    /**
     * 执行索引变更（历史记录恢复时调用）
     */
    private executeIndexChange(newIndex: number): void {
        console.log(`🎯 执行索引变更: ${this.className} 目标索引 ${newIndex}`);

        const parent = this.parent;
        if (!parent) {
            console.log(`🎯 跳过索引调整: ${this.className} 没有父节点`);
            return;
        }

        // 🎯 修复：允许 newIndex 为 -1，表示删除状态
        if (newIndex < -1) {
            console.log(`🎯 跳过索引调整: ${this.className} 索引无效 ${newIndex}`);
            return;
        }

        const currentIndex = parent.children.indexOf(this);
        console.log(`🎯 当前状态: ${this.className} 在 ${parent.className} 的索引 ${currentIndex}`);

        // 🎯 修复：如果是恢复删除的节点（newIndex >= 0），确保正确插入到指定位置
        if (newIndex >= 0) {
            if (currentIndex === -1) {
                // 🎯 节点不在父节点中，需要插入到指定位置
                // 这通常发生在恢复删除的节点时
                const safeIndex = Math.min(newIndex, parent.children.length);
                parent.children.splice(safeIndex, 0, this);
                console.log(`🎯 插入节点: ${this.className} 到 ${parent.className}[${safeIndex}] (目标: ${newIndex})`);
            } else if (currentIndex !== newIndex) {
                // 🎯 节点在父节点中，需要移动到新位置
                parent.children.splice(currentIndex, 1); // 先移除

                // 🎯 重要：如果目标位置在当前位置之后，需要调整索引
                const adjustedIndex = newIndex > currentIndex ? newIndex - 1 : newIndex;
                const safeIndex = Math.min(adjustedIndex, parent.children.length);
                parent.children.splice(safeIndex, 0, this); // 插入到新位置

                console.log(`🎯 移动节点: ${this.className} 从 ${currentIndex} 到 ${safeIndex} (目标: ${newIndex}, 调整后: ${adjustedIndex})`);
            } else {
                console.log(`🎯 索引无需调整: ${this.className} 已在正确位置 ${newIndex}`);
                return;
            }
        } else {
            // newIndex === -1，表示删除状态
            if (currentIndex !== -1) {
                parent.children.splice(currentIndex, 1);
                console.log(`🎯 从索引移除: ${this.className} 从 ${parent.className}[${currentIndex}]`);
            }
        }

        // 验证最终位置
        const finalIndex = parent.children.indexOf(this);
        console.log(`🎯 索引调整完成: ${this.className} 最终位置 ${finalIndex}`);

        // 同步到原始对象
        this.syncChildrenOrder(parent);
    }

    /**
     * 执行删除操作（历史记录恢复时调用）
     */
    private executeDelete(): void {
        console.log(`🎯 执行删除: ${this.className}`);
        // 这里可以实现删除逻辑，但通常删除是通过其他方式处理的
        // 因为删除操作会记录删除前的状态，撤销时会恢复
    }

    /**
     * 执行恢复操作（历史记录恢复时调用）
     */
    private executeRestore(): void {
        console.log(`🎯 执行恢复: ${this.className}`);
        // 恢复被删除的节点，通常通过恢复父子关系来实现
    }

    /**
     * 执行移除创建的节点（历史记录恢复时调用）
     */
    private executeRemoveCreated(): void {
        console.log(`🎯 移除创建的节点: ${this.className}`);

        // 从父节点移除
        const parent = this.parent;
        if (parent) {
            const index = parent.children.indexOf(this);
            if (index !== -1) {
                parent.children.splice(index, 1);
            }
        }

        // 销毁对象
        this.destroy();
    }

    /**
     * 同步到原始对象（历史记录恢复时调用）
     */
    private syncToOriginalObject(): void {
        const thisOriginal = this.getOriginalObject();
        const parent = this.parent;

        if (!thisOriginal) return;

        // 从原始父对象移除
        if (thisOriginal.parent && typeof thisOriginal.parent.removeChild === 'function') {
            thisOriginal.parent.removeChild(thisOriginal);
        }

        // 添加到新的原始父对象
        if (parent) {
            const parentOriginal = parent.getOriginalObject();
            if (parentOriginal && typeof parentOriginal.addChild === 'function') {
                parentOriginal.addChild(thisOriginal);
                console.log(`🎯 同步到原始对象: ${this.className} → ${parent.className}`);
            }
        }
    }
}

// 将 BaseObjectModel 暴露到全局，以便 HistoryManager 可以访问
if (typeof window !== 'undefined') {
    (window as any).BaseObjectModel = BaseObjectModel;
}