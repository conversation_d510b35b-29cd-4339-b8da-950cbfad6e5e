<script lang="ts">
  import type { ImageModel } from '../../../type/ui/imageModel.svelte';
  import { selectImageResourcePath } from '../../../logics/resourceLoader/resourceSelector';
  import { imageCache } from '../../../utils/imageCache';
  import { SmartCropAPI, type DetectedUIElement, type CropOptions } from '../../../utils/smartCrop';
  import { tick } from 'svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import LabelInput from '../../../components/LabelInput.svelte';

  interface Props {
    model: ImageModel;
  }

  let { model }: Props = $props();
  // 响应式状态来跟踪bitmap URL变化
  let bitmapUrl = $state('');

  // Canvas 相关
  let previewCanvas: HTMLCanvasElement;
  let hoveredRegionIndex = $state(-1);

  // 智能裁切相关状态 - 使用模型的响应式字段
  let isAnalyzing = $state(false);
  // detectedElements 和 showSmartCropResults 现在使用 model 的响应式字段
  let analysisError = $state<string | null>(null);
  let processingTime = $state(0);

  // 预览图缓存
  let previewCache = new Map<string, string>();
  let lastCanvasState = $state('');

  // 用于触发列表重新渲染的计数器
  let listRefreshTrigger = $state(0);

  // 监听model变化并强制检查bitmap和智能裁切状态
  $effect(() => {
    // 监听model的变化
    model.imagePath;
    model.updateCounter; // 监听ImageModel的updateCounter

    const originalObject = (model as any)._originalObject;
    const bitmap = originalObject?.bitmap;
    if (!bitmap) {
      console.log('No bitmap found, originalObject:', originalObject);
      bitmapUrl = '';
      return;
    }

    // 智能裁切状态现在通过模型的 syncRegionsFromOriginal 自动恢复

    console.log('🔍 属性面板检查bitmap:', {
      bitmap: bitmap,
      url: bitmap.url,
      loadingState: bitmap._loadingState,
      hasUrl: !!bitmap.url,
      urlType: typeof bitmap.url,
      updateCounter: model.updateCounter
    });

    // 检查当前URL
    const currentUrl = bitmap.url || '';

    if (currentUrl.startsWith('blob:')) {
      console.log('Found blob URL:', currentUrl);
      bitmapUrl = currentUrl;
    } else {
      console.log('No blob URL yet, current url:', currentUrl);
      bitmapUrl = '';

      // 如果bitmap正在加载，添加加载监听器
      if (bitmap._loadingState === 'loading' && typeof bitmap.addLoadListener === 'function') {
        const loadListener = () => {
          console.log('Bitmap loaded, checking URL again:', bitmap.url);
          if (bitmap.url && bitmap.url.startsWith('blob:')) {
            bitmapUrl = bitmap.url;
            // updateCounter已经通过ImageModel的_triggerUpdate()触发了
          }
        };

        bitmap.addLoadListener(loadListener);

        // 清理函数
        return () => {
          if (typeof bitmap.removeLoadListener === 'function') {
            bitmap.removeLoadListener(loadListener);
          }
        };
      }
    }
  });

  // 直接使用model的响应式数据
  let imageInfo = $derived(model.getImageInfo());
  let shouldShowImage = $derived(imageInfo.hasImage && bitmapUrl && bitmapUrl.startsWith('blob:'));

  // 调试 shouldShowImage 的计算
  $effect(() => {
    console.log('🔍 shouldShowImage 计算:', {
      imageInfo,
      hasImage: imageInfo.hasImage,
      bitmapUrl,
      hasBitmapUrl: !!bitmapUrl,
      isBlob: bitmapUrl && bitmapUrl.startsWith('blob:'),
      shouldShowImage
    });
  });

  // 手风琴展开状态
  let isExpanded = $state(true);



  // 处理图片选择
  async function handleSelectImage() {
    try {
      const selectedPath = await selectImageResourcePath('选择图片文件');
      if (selectedPath && selectedPath !== '用户取消选择') {
        model.imagePath = selectedPath;
        console.log('选择的图片路径:', selectedPath);
      }
    } catch (error) {
      console.error('选择图片失败:', error);
    }
  }

  // 生成网格区域
  function generateGrid() {
    model.generateGridRegions();
    // 生成网格后立即重绘
    drawCanvas();
  }

  // 重置为默认区域
  function resetToDefault() {
    model.resetToDefaultRegion();
    // 重置智能裁切结果
    model.detectedElements = [];
    model.showSmartCropResults = false;
    analysisError = null;
    // 重置后立即重绘
    drawCanvas().catch(error => {
      console.error('重置后重绘失败:', error);
    });
  }

  // 智能裁切分析
  async function performSmartCrop() {
    if (!previewCanvas || !bitmapUrl) {
      analysisError = '没有可分析的图像';
      return;
    }

    isAnalyzing = true;
    analysisError = null;
    model.detectedElements = [];

    try {
      console.log('🔍 开始智能裁切分析...');

      // 获取Canvas的图像数据
      const imageData = SmartCropAPI.canvasToBase64(previewCanvas);

      console.log('🔧 智能裁切分析图片信息:', {
        canvasSize: `${previewCanvas.width}x${previewCanvas.height}`,
        originalImageSize: '将在分析后确定'
      });

      // 获取默认选项
      const options = await SmartCropAPI.getDefaultOptions();

      // 执行分析
      const result = await SmartCropAPI.analyzeImage(imageData, options);

      if (result.success) {
        model.detectedElements = result.elements;
        processingTime = result.processing_time;
        model.showSmartCropResults = true;

        console.log('✅ 智能裁切分析成功', {
          elementsCount: model.detectedElements.length,
          processingTime: SmartCropAPI.formatProcessingTime(processingTime),
          elements: model.detectedElements.map(e => `${e.bounds.x},${e.bounds.y},${e.bounds.width},${e.bounds.height}`)
        });

        // 应用检测结果到模型
        applySmartCropResults();
      } else {
        analysisError = result.error || '分析失败';
        console.error('❌ 智能裁切分析失败:', result.error);
      }
    } catch (error) {
      analysisError = error instanceof Error ? error.message : '未知错误';
      console.error('❌ 智能裁切分析异常:', error);
    } finally {
      isAnalyzing = false;
    }
  }

  // 从列表中选择区域
  function selectRegionFromList(index: number) {
    if (!model.isMultiRegion) {
      // 如果还没有应用智能裁切结果，先应用
      applySmartCropResults();
    }

    // 设置当前区域
    if (index >= 0 && index < model.regions.length) {
      console.log('🎯 从列表选择区域:', index, model.regions[index]);
      model.setCurrentRegion(index);
    }
  }

  // 预加载的图像对象
  let loadedImage: HTMLImageElement | null = null;

  // 加载图像
  async function loadImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error('图像加载失败'));
      img.src = url;
    });
  }

  // 图像加载状态
  let imageLoadingState = $state<'idle' | 'loading' | 'loaded' | 'error'>('idle');

  // 监听bitmapUrl变化，预加载图像
  $effect(() => {
    if (bitmapUrl) {
      imageLoadingState = 'loading';
      loadedImage = null; // 立即清空当前图像
      previewCache.clear(); // 清空缓存

      console.log('🔄 开始加载图像:', bitmapUrl);

      loadImage(bitmapUrl).then(img => {
        loadedImage = img;
        imageLoadingState = 'loaded';
        // 触发列表重新渲染
        listRefreshTrigger++;
        console.log('🔄 图像预加载完成，触发列表刷新:', {
          url: bitmapUrl,
          size: `${img.naturalWidth}x${img.naturalHeight}`,
          refreshTrigger: listRefreshTrigger
        });
      }).catch(error => {
        console.error('图像预加载失败:', error);
        loadedImage = null;
        imageLoadingState = 'error';
      });
    } else {
      loadedImage = null;
      imageLoadingState = 'idle';
    }
  });

  // 生成区域预览图片（使用预加载的图片，支持智能裁切和网格裁切）
  function generateRegionPreview(region: any, isSmartCrop: boolean = false): string {
    // 访问刷新触发器以确保响应式更新
    listRefreshTrigger;

    console.log('🔍 generateRegionPreview 调用:', {
      region,
      isSmartCrop,
      bitmapUrl: !!bitmapUrl,
      loadedImage: !!loadedImage,
      imageLoadingState,
      refreshTrigger: listRefreshTrigger,
      modelRegionsLength: model.regions.length,
      originalObjectRegions: model._originalObject?.regions?.length || 0
    });

    // 如果图像正在加载，返回空字符串（会显示占位符）
    if (imageLoadingState === 'loading') {
      console.log('🔍 图像正在加载中，稍后重试');
      return '';
    }

    if (!bitmapUrl || !loadedImage || imageLoadingState !== 'loaded') {
      console.log('🔍 预览图生成失败: 缺少必要条件', {
        bitmapUrl: !!bitmapUrl,
        loadedImage: !!loadedImage,
        imageLoadingState
      });
      return '';
    }

    // 获取原始图片对象
    const originalObject = model._originalObject;
    if (!originalObject || !originalObject.sourceBitmap) {
      return '';
    }

    // 生成缓存键
    const regionKey = isSmartCrop
      ? `smart_${JSON.stringify(region.bounds || region)}`
      : `grid_${region.sx}_${region.sy}_${region.sw}_${region.sh}`;
    const cacheKey = `${regionKey}_${bitmapUrl}`;

    // 检查缓存
    if (previewCache.has(cacheKey)) {
      return previewCache.get(cacheKey)!;
    }

    try {
      // 创建临时Canvas来绘制区域预览
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) return '';

      // 设置预览尺寸
      const previewSize = 48;
      const pixelRatio = window.devicePixelRatio || 1;
      const scaledSize = previewSize * pixelRatio;

      tempCanvas.width = scaledSize;
      tempCanvas.height = scaledSize;
      tempCanvas.style.width = previewSize + 'px';
      tempCanvas.style.height = previewSize + 'px';

      // 缩放上下文以匹配设备像素比
      tempCtx.scale(pixelRatio, pixelRatio);

      // 直接使用原始图片坐标
      let sourceX, sourceY, sourceW, sourceH;

      if (isSmartCrop) {
        // 智能裁切：优先使用sx属性（来自model.regions），否则使用bounds
        if (region.sx !== undefined) {
          sourceX = region.sx;
          sourceY = region.sy;
          sourceW = region.sw;
          sourceH = region.sh;
        } else {
          // 来自detectedElements，使用bounds
          const bounds = region.bounds;
          sourceX = bounds.x;
          sourceY = bounds.y;
          sourceW = bounds.width;
          sourceH = bounds.height;
        }
      } else {
        // 网格裁切：直接使用原始图片坐标
        sourceX = region.sx;
        sourceY = region.sy;
        sourceW = region.sw;
        sourceH = region.sh;
      }

      // 确保坐标在原始图片范围内
      const originalWidth = loadedImage.naturalWidth;
      const originalHeight = loadedImage.naturalHeight;
      sourceX = Math.max(0, Math.min(sourceX, originalWidth - 1));
      sourceY = Math.max(0, Math.min(sourceY, originalHeight - 1));
      sourceW = Math.max(1, Math.min(sourceW, originalWidth - sourceX));
      sourceH = Math.max(1, Math.min(sourceH, originalHeight - sourceY));

      console.log(`🔍 生成预览图 ${isSmartCrop ? '智能裁切' : '网格'}: 原始图片尺寸(${originalWidth}x${originalHeight}), 源区域(${sourceX.toFixed(1)},${sourceY.toFixed(1)},${sourceW.toFixed(1)},${sourceH.toFixed(1)})`);

      // 检查源区域是否有效
      if (sourceW <= 0 || sourceH <= 0) {
        console.log('🔍 预览图生成失败: 源区域尺寸无效', { sourceW, sourceH });
        return '';
      }

      // 填充背景
      tempCtx.fillStyle = '#1a1a1a';
      tempCtx.fillRect(0, 0, previewSize, previewSize);

      // 计算保持宽高比的绘制尺寸
      const aspectRatio = sourceW / sourceH;
      let drawW = previewSize;
      let drawH = previewSize;
      let drawX = 0;
      let drawY = 0;

      if (aspectRatio > 1) {
        // 宽度较大，以宽度为准
        drawH = previewSize / aspectRatio;
        drawY = (previewSize - drawH) / 2;
      } else {
        // 高度较大，以高度为准
        drawW = previewSize * aspectRatio;
        drawX = (previewSize - drawW) / 2;
      }

      // 设置高质量绘制
      tempCtx.imageSmoothingEnabled = true;
      tempCtx.imageSmoothingQuality = 'high';

      // 从预加载的HTML图像元素绘制区域，保持宽高比
      tempCtx.drawImage(
        loadedImage,
        sourceX, sourceY, sourceW, sourceH,
        drawX, drawY, drawW, drawH
      );

      console.log(`🔍 ${isSmartCrop ? '智能裁切' : '网格'}预览图绘制成功 - 保持宽高比: ${aspectRatio.toFixed(2)}, 绘制尺寸: ${drawW.toFixed(1)}x${drawH.toFixed(1)}`);

      // 添加边框以便识别
      tempCtx.strokeStyle = isSmartCrop ? '#FF9800' : '#4CAF50';
      tempCtx.lineWidth = 2;
      tempCtx.strokeRect(0, 0, previewSize, previewSize);

      // 返回base64数据URL
      const dataUrl = tempCanvas.toDataURL('image/png');

      // 保存到缓存
      previewCache.set(cacheKey, dataUrl);

      return dataUrl;
    } catch (error) {
      console.error('生成区域预览失败:', error);
      return '';
    }
  }


  // 应用智能裁切结果
  function applySmartCropResults() {
    if (model.detectedElements.length === 0 || !previewCanvas) return;

    // 🔧 获取原始对象引用
    const originalObject = model._originalObject;

    // 🔧 检查检测到的坐标是基于Canvas还是原始图片
    const firstElement = model.detectedElements[0];
    let needsCoordinateConversion = false;

    if (originalObject && originalObject.sourceBitmap && firstElement) {
      // 如果检测到的最大坐标超过Canvas尺寸，说明已经是原始图片坐标
      const maxDetectedX = Math.max(...model.detectedElements.map(e => e.bounds.x + e.bounds.width));
      const maxDetectedY = Math.max(...model.detectedElements.map(e => e.bounds.y + e.bounds.height));

      needsCoordinateConversion = maxDetectedX <= previewCanvas.width && maxDetectedY <= previewCanvas.height;

      console.log('🔧 坐标系统检测:', {
        canvasSize: `${previewCanvas.width}x${previewCanvas.height}`,
        originalSize: `${originalObject.sourceBitmap.width}x${originalObject.sourceBitmap.height}`,
        maxDetected: `${maxDetectedX}x${maxDetectedY}`,
        needsConversion: needsCoordinateConversion,
        firstElementBounds: `${firstElement.bounds.x},${firstElement.bounds.y},${firstElement.bounds.width},${firstElement.bounds.height}`
      });
    }

    const regions = model.detectedElements.map((element, index) => {
      let finalX, finalY, finalW, finalH;

      if (needsCoordinateConversion && originalObject && originalObject.sourceBitmap) {
        // 需要从Canvas坐标转换为原始图片坐标
        const scaleX = originalObject.sourceBitmap.width / previewCanvas.width;
        const scaleY = originalObject.sourceBitmap.height / previewCanvas.height;

        finalX = Math.round(element.bounds.x * scaleX);
        finalY = Math.round(element.bounds.y * scaleY);
        finalW = Math.round(element.bounds.width * scaleX);
        finalH = Math.round(element.bounds.height * scaleY);

        if (index < 3) {
          console.log(`🔧 区域${index}坐标转换:`, {
            Canvas: `${element.bounds.x},${element.bounds.y},${element.bounds.width},${element.bounds.height}`,
            原始图片: `${finalX},${finalY},${finalW},${finalH}`,
            缩放比例: `${scaleX.toFixed(3)}x${scaleY.toFixed(3)}`
          });
        }
      } else {
        // 直接使用检测到的坐标（已经是原始图片坐标）
        finalX = element.bounds.x;
        finalY = element.bounds.y;
        finalW = element.bounds.width;
        finalH = element.bounds.height;

        if (index < 3) {
          console.log(`🔧 区域${index}直接使用:`, {
            坐标: `${finalX},${finalY},${finalW},${finalH}`,
            说明: '检测坐标已经是原始图片坐标'
          });
        }
      }

      return {
        id: element.id,
        label: `${SmartCropAPI.getElementTypeDisplayName(element.element_type)}_${index + 1}`,
        sx: finalX,
        sy: finalY,
        sw: finalW,
        sh: finalH,
        gridIndex: index
      };
    });

    // 更新模型的区域数据
    model.regions = regions;

    // 🔧 关键修复：同步regions到原始对象，避免被syncRegionsFromOriginal覆盖
    if (model._originalObject) {
      model._originalObject.regions = [...regions];
      // 🔧 新增：保存智能裁切的元数据到原始对象
      model._originalObject.detectedElements = [...model.detectedElements];
      model._originalObject.showSmartCropResults = model.showSmartCropResults;
      console.log('🔧 同步regions和智能裁切数据到原始对象:', {
        regionsCount: model._originalObject.regions.length,
        hasSmartCropData: !!model._originalObject.smartCropData
      });
    }

    // 如果有多个区域，设置为多区域模式

      model.gridRows = 1;
      model.gridCols = 1;
    

    // 🔧 关键：调用setCurrentRegion来正确同步数据
    console.log('🔍 调用setCurrentRegion前 regions数量:', model.regions.length);
    model.setCurrentRegion(0);
    console.log('🔍 调用setCurrentRegion后 regions数量:', model.regions.length);

    // 🔍 检查坐标系统
    if (originalObject && originalObject.sourceBitmap) {
      console.log('🔍 坐标系统检查:', {
        canvasSize: `${previewCanvas.width}x${previewCanvas.height}`,
        originalImageSize: `${originalObject.sourceBitmap.width}x${originalObject.sourceBitmap.height}`,
        scaleX: previewCanvas.width / originalObject.sourceBitmap.width,
        scaleY: previewCanvas.height / originalObject.sourceBitmap.height,
        firstDetectedElement: model.detectedElements[0]?.bounds,
        firstRegion: regions[0]
      });
    }

    console.log('🎯 应用智能裁切结果 - 设置前', {
      regionsCount: regions.length,
      gridSize: `${model.gridRows}x${model.gridCols}`,
      firstRegion: regions[0],
      canvasSize: { width: previewCanvas.width, height: previewCanvas.height },
      regions: regions.map(r => `${r.sx},${r.sy},${r.sw},${r.sh}`)
    });

    console.log('🔍 模型regions设置前:', model.regions.length);
    console.log('🔍 模型regions设置后:', model.regions.length);
    console.log('🔍 模型regions内容:', model.regions.map(r => `${r.sx},${r.sy},${r.sw},${r.sh}`));

    // 重绘Canvas显示结果
    drawCanvas().catch(error => {
      console.error('应用结果后重绘失败:', error);
    });
  }

  // Canvas 绘制相关函数
  async function drawCanvas() {
    if (!previewCanvas || !bitmapUrl) return;

    const ctx = previewCanvas.getContext('2d');
    if (!ctx) return;

    try {
      // ✅ 使用图片缓存工具，避免重复加载
      const img = await imageCache.ensureImageLoaded(bitmapUrl);

      // 计算Canvas尺寸，保持宽高比
      const maxWidth = 300;
      const maxHeight = 200;
      const aspectRatio = img.width / img.height;

      let canvasWidth, canvasHeight;
      if (aspectRatio > maxWidth / maxHeight) {
        canvasWidth = maxWidth;
        canvasHeight = maxWidth / aspectRatio;
      } else {
        canvasHeight = maxHeight;
        canvasWidth = maxHeight * aspectRatio;
      }

      previewCanvas.width = canvasWidth;
      previewCanvas.height = canvasHeight;

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // ✅ 直接绘制已加载的图片，无需等待
      ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);

      // 绘制网格线和区域
      drawGridAndRegions(ctx, canvasWidth, canvasHeight);

      // 更新Canvas状态，清空缓存
      const newCanvasState = `${bitmapUrl}_${canvasWidth}_${canvasHeight}_${Date.now()}`;
      if (newCanvasState !== lastCanvasState) {
        lastCanvasState = newCanvasState;
        previewCache.clear();
        console.log('🔄 Canvas状态更新，清空预览图缓存');
      }

    } catch (error) {
      console.error('Canvas 绘制失败:', error);
    }
  }

  function drawGridAndRegions(ctx: CanvasRenderingContext2D, canvasWidth: number, canvasHeight: number) {
    const rows = model.gridRows;
    const cols = model.gridCols;

    // 如果显示智能裁切结果，绘制检测到的区域
    if (model.showSmartCropResults && model.detectedElements.length > 0) {
      drawSmartCropRegions(ctx, canvasWidth, canvasHeight);
      return;
    }

    // 当网格大于1x1时就绘制网格线（预览模式）
    if (rows <= 1 && cols <= 1) return;

    // 绘制网格线
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);

    // 垂直线
    for (let i = 1; i < cols; i++) {
      const x = (canvasWidth / cols) * i;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvasHeight);
      ctx.stroke();
    }

    // 水平线
    for (let i = 1; i < rows; i++) {
      const y = (canvasHeight / rows) * i;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvasWidth, y);
      ctx.stroke();
    }

    // 只有在多区域模式下才绘制区域高亮
    if (model.isMultiRegion) {
      // 绘制当前选中区域
      if (model.currentRegionIndex >= 0 && model.currentRegionIndex < rows * cols) {
        const cellWidth = canvasWidth / cols;
        const cellHeight = canvasHeight / rows;
        const row = Math.floor(model.currentRegionIndex / cols);
        const col = model.currentRegionIndex % cols;

        ctx.strokeStyle = '#ff0000';
        ctx.lineWidth = 2;
        ctx.setLineDash([]);
        ctx.strokeRect(col * cellWidth, row * cellHeight, cellWidth, cellHeight);

        // 填充半透明背景
        ctx.fillStyle = 'rgba(255, 0, 0, 0.2)';
        ctx.fillRect(col * cellWidth, row * cellHeight, cellWidth, cellHeight);
      }

      // 绘制悬停区域
      if (hoveredRegionIndex >= 0 && hoveredRegionIndex < rows * cols && hoveredRegionIndex !== model.currentRegionIndex) {
        const cellWidth = canvasWidth / cols;
        const cellHeight = canvasHeight / rows;
        const row = Math.floor(hoveredRegionIndex / cols);
        const col = hoveredRegionIndex % cols;

        ctx.strokeStyle = '#ffff00';
        ctx.lineWidth = 1;
        ctx.setLineDash([]);
        ctx.strokeRect(col * cellWidth, row * cellHeight, cellWidth, cellHeight);

        // 填充半透明背景
        ctx.fillStyle = 'rgba(255, 255, 0, 0.1)';
        ctx.fillRect(col * cellWidth, row * cellHeight, cellWidth, cellHeight);
      }
    }
  }

  // 绘制智能裁切检测到的区域
  function drawSmartCropRegions(ctx: CanvasRenderingContext2D, canvasWidth: number, canvasHeight: number) {
    if (!model.detectedElements.length) return;

    // 获取原始图片尺寸用于坐标转换
    const originalObject = model._originalObject;
    if (!originalObject || !originalObject.sourceBitmap) {
      console.log('⚠️ 无法获取原始图片信息，跳过绘制');
      return;
    }

    // 🔧 修复坐标转换逻辑
    // 后端返回的坐标是基于分析时的Canvas尺寸，不是原始图像尺寸
    // 需要检查分析时的Canvas尺寸和当前绘制时的Canvas尺寸是否一致

    console.log('🎨 绘制智能裁切区域:', {
      当前Canvas尺寸: `${canvasWidth}x${canvasHeight}`,
      分析时Canvas尺寸: `${previewCanvas.width}x${previewCanvas.height}`,
      元素数量: model.detectedElements.length
    });

    // 计算从分析时Canvas到当前绘制Canvas的缩放比例
    const scaleX = canvasWidth / previewCanvas.width;
    const scaleY = canvasHeight / previewCanvas.height;

    model.detectedElements.forEach((element, index) => {
      const bounds = element.bounds;

      // 如果分析时和绘制时的Canvas尺寸一致，直接使用坐标
      // 否则需要按比例缩放
      const x = Math.round(bounds.x * scaleX);
      const y = Math.round(bounds.y * scaleY);
      const width = Math.round(bounds.width * scaleX);
      const height = Math.round(bounds.height * scaleY);

      if (index < 3) {
        console.log(`🔍 元素${index}坐标转换:`, {
          原始: `${bounds.x},${bounds.y},${bounds.width},${bounds.height}`,
          Canvas: `${x},${y},${width},${height}`
        });
      }

      // 获取元素类型对应的颜色
      const color = SmartCropAPI.getElementTypeColor(element.element_type);

      // 绘制边框（确保像素对齐）
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.setLineDash([]);
      ctx.strokeRect(x + 0.5, y + 0.5, width - 1, height - 1); // 亚像素对齐

      // 绘制半透明填充
      ctx.fillStyle = color + '20'; // 添加透明度
      ctx.fillRect(x, y, width, height);
    });
  }

  // 监听变化并重绘Canvas
  $effect(() => {
    console.log('🔍 Canvas 绘制条件检查:', {
      hasBitmapUrl: !!bitmapUrl,
      bitmapUrl: bitmapUrl,
      hasPreviewCanvas: !!previewCanvas,
      canDraw: !!(bitmapUrl && previewCanvas)
    });

    if (bitmapUrl) {
      console.log('🎨 有 bitmapUrl，等待 Canvas 绑定');
      // 使用 tick() 确保 DOM 完全更新后再检查 Canvas
      tick().then(() => {
        if (previewCanvas) {
          console.log('✅ Canvas 已绑定，开始绘制');
          drawCanvas().catch(error => {
            console.error('Canvas 重绘失败:', error);
          });
        } else {
          console.log('⏳ Canvas 还未绑定，等待下次更新');
        }
      });
    } else {
      console.log('❌ 没有 bitmapUrl');
    }
  });

  // 专门监听网格变化的 effect
  $effect(() => {
    // 显式读取这些属性以确保监听
    const gridRows = model.gridRows;
    const gridCols = model.gridCols;
    const currentRegionIndex = model.currentRegionIndex;

    // console.log('🔄 网格属性变化检测:', {
    //   gridRows,
    //   gridCols,
    //   currentRegionIndex,
    //   hasCanvas: !!previewCanvas
    // });

    if (previewCanvas) {
      console.log('✅ 触发Canvas重绘');
      // 使用 tick() 确保状态更新完成后再绘制
      tick().then(() => {
        drawCanvas().catch(error => {
          console.error('Canvas 重绘失败:', error);
        });
      });
    } else {
      console.log('⏳ Canvas未准备好，跳过重绘');
    }
  });

  // Canvas 元素绑定后立即尝试绘制
  $effect(() => {
    if (previewCanvas) {
      console.log('🎯 Canvas 元素已绑定，立即尝试绘制');

      // 删除Canvas点击事件监听器，统一使用列表选择

      if (bitmapUrl) {
        console.log('🎨 有 bitmapUrl，开始绘制');
        // 使用 tick() 确保 DOM 完全更新后再绘制
        tick().then(() => {
          drawCanvas().catch(error => {
            console.error('Canvas 初始绘制失败:', error);
          });
        });
      } else {
        console.log('⏳ 等待 bitmapUrl...');
      }
    }
  });


</script>

<AccordionPanel
  title="UI Image 属性"
  icon="🖼️"
  badge={shouldShowImage ? '有图片' : '无图片'}
  badgeVariant={shouldShowImage ? 'active' : 'inactive'}
  bind:expanded={isExpanded}
>
  {#if shouldShowImage}
    <!-- 图片预览 -->
    <div class="image-layout">
      <div class="image-preview">
        <canvas
          bind:this={previewCanvas}
          class="preview-canvas"
        ></canvas>
      </div>
    </div>

    <!-- 更换图片按钮 -->
    <div class="image-actions">
      <button
        type="button"
        onclick={handleSelectImage}
        class="change-image-button"
      >
        🔄 更换图片
      </button>
    </div>
  {:else}
    <!-- 无图片状态 -->
    <div class="no-image-section">
      <div class="no-image-content">
        <div class="no-image-icon">🖼️</div>
        <div class="no-image-text">
          <span class="no-image-title">无图片</span>
          <span class="no-image-subtitle">点击选择图片</span>
        </div>
        <button
          type="button"
          onclick={handleSelectImage}
          class="select-image-button"
        >
          选择图片
        </button>
      </div>
    </div>
  {/if}
</AccordionPanel>

<!-- 图片裁切面板 -->
<AccordionPanel title="图片裁切" expanded={false}>
  <div class="crop-section">
    <!-- 当前状态显示 -->
    <div class="crop-status">
      <div class="status-info">
        <span class="status-label">裁切状态:</span>
        <span class="status-value">
          {#if model.isMultiRegion}
            多区域裁切 ({model.regions.length} 个区域)
          {:else}
            单区域 (完整图片)
          {/if}
        </span>
      </div>

      {#if model.currentRegion}
        <div class="current-region">
          <span class="region-label">当前区域:</span>
          <span class="region-info">
            {model.currentRegion.label}
            ({model.currentRegion.sw}×{model.currentRegion.sh})
          </span>
        </div>
      {/if}
    </div>

    <!-- 网格设置 -->
    <div class="grid-settings">
      <div class="grid-inputs">
        <PropertyContainer>
          <Label text="网格行数:" />
          <LabelInput
            bind:value={model.gridRows}
            type="number"
            min={1}
            max={20}
            targetObject={model}
            fieldName="gridRows"
            name="网格行数"
          />
        </PropertyContainer>
        <PropertyContainer>
          <Label text="网格列数:" />
          <LabelInput
            bind:value={model.gridCols}
            type="number"
            min={1}
            max={20}
            targetObject={model}
            fieldName="gridCols"
            name="网格列数"
          />
        </PropertyContainer>
      </div>
      <div class="grid-actions">
        <button class="action-btn" onclick={() => generateGrid()}>
          🔄 生成网格 ({model.gridRows}×{model.gridCols})
        </button>
        <button
          class="action-btn smart-crop"
          onclick={() => performSmartCrop()}
          disabled={isAnalyzing || !shouldShowImage}
        >
          {#if isAnalyzing}
            🔄 分析中...
          {:else}
            🤖 智能裁切
          {/if}
        </button>
        {#if model.isMultiRegion || model.showSmartCropResults}
          <button class="action-btn secondary" onclick={() => resetToDefault()}>
            ↩️ 重置为完整图片
          </button>
        {/if}
      </div>
    </div>
  </div>
</AccordionPanel>

<!-- 网格区域列表面板 -->
{#if model.isMultiRegion && !model.showSmartCropResults && model.regions.length > 1}
<AccordionPanel title="网格区域列表" expanded={true}>
  <div class="regions-list-container">
    <div class="results-summary">
      <div class="summary-info">
        <span class="summary-label">网格区域:</span>
        <span class="summary-value">{model.regions.length} 个区域</span>
      </div>
      <div class="summary-info">
        <span class="summary-label">当前选中:</span>
        <span class="summary-value">区域 {model.currentRegionIndex + 1}</span>
      </div>
    </div>

    <div class="elements-list">
      {#each model.regions as region, index}
        <button
          class="element-item {model.currentRegionIndex === index ? 'selected' : ''}"
          onclick={() => selectRegionFromList(index)}
          tabindex="0"
        >
          <div class="element-content">
            <!-- 区域预览图片 -->
            <div class="element-preview">
              {#if true}
                {@const previewSrc = generateRegionPreview(region, false)}
                {#if previewSrc}
                  <img
                    src={previewSrc}
                    alt="区域预览"
                    class="preview-image"
                  />
                {:else}
                  <div class="preview-placeholder">
                    <div class="grid-indicator">
                      {index + 1}
                    </div>
                  </div>
                {/if}
              {/if}
            </div>

            <!-- 区域信息 -->
            <div class="element-info">
              <div class="element-header">
                <span class="element-name">
                  网格区域 {index + 1}
                </span>
                <span class="element-confidence">
                  {Math.floor((index / model.regions.length) * 100)}%
                </span>
              </div>
              <div class="element-details">
                <span class="element-size">
                  {region.sw}×{region.sh}
                </span>
                <span class="element-position">
                  ({region.sx}, {region.sy})
                </span>
              </div>
              {#if model.currentRegionIndex === index}
                <div class="selected-indicator">
                  ✓ 当前选中
                </div>
              {/if}
            </div>
          </div>
        </button>
      {/each}
    </div>
  </div>
</AccordionPanel>
{/if}

<!-- 智能裁切结果面板 -->
{#if model.showSmartCropResults || analysisError}
<AccordionPanel title="智能裁切结果" expanded={true}>
  <div class="smart-crop-results">
    {#if analysisError}
      <div class="error-message">
        <span class="error-icon">❌</span>
        <span class="error-text">{analysisError}</span>
      </div>
    {:else if model.showSmartCropResults && model.regions.length > 0}
      <div class="results-summary">
        <div class="summary-info">
          <span class="summary-label">检测结果:</span>
          <span class="summary-value">{model.regions.length} 个UI元素</span>
        </div>
        <div class="summary-info">
          <span class="summary-label">当前选中:</span>
          <span class="summary-value">
            {model.currentRegionIndex >= 0 ? `元素 ${model.currentRegionIndex + 1}` : '未选择'}
          </span>
        </div>
        <div class="summary-info">
          <span class="summary-label">处理时间:</span>
          <span class="summary-value">{SmartCropAPI.formatProcessingTime(processingTime)}</span>
        </div>
      </div>

      <div class="elements-list">
        {#each model.regions as region, index}
          <button
            class="element-item {model.currentRegionIndex === index ? 'selected' : ''}"
            onclick={() => selectRegionFromList(index)}
            tabindex="0"
          >
            <div class="element-content">
              <!-- 区域预览图片 -->
              <div class="element-preview">
                {#if true}
                  {@const previewSrc = generateRegionPreview(region, true)}
                  {#if previewSrc}
                    <img
                      src={previewSrc}
                      alt="区域预览"
                      class="preview-image"
                    />
                  {:else}
                    <div class="preview-placeholder">
                      <span class="element-name-short">
                        {region.elementType ? SmartCropAPI.getElementTypeDisplayName(region.elementType).substring(0, 2) : 'UI'}
                      </span>
                    </div>
                  {/if}
                {/if}
              </div>

              <!-- 区域信息 -->
              <div class="element-info">
                <div class="element-header">
                  <span class="element-name">
                    智能区域 {index + 1}
                  </span>
                  <span class="element-confidence">
                    100%
                  </span>
                </div>
                <div class="element-details">
                  <span class="element-size">
                    {region.sw}×{region.sh}
                  </span>
                  <span class="element-position">
                    ({region.sx}, {region.sy})
                  </span>
                </div>
                {#if model.currentRegionIndex === index}
                  <div class="selected-indicator">
                    ✓ 当前选中
                  </div>
                {/if}
              </div>
            </div>
          </button>
        {/each}
      </div>
    {:else}
      <div class="no-results">
        <span class="no-results-icon">🔍</span>
        <span class="no-results-text">未检测到UI元素</span>
      </div>
    {/if}
  </div>
</AccordionPanel>
{/if}
<style>
  /* 图片布局容器 */
  .image-layout {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
  }

  /* 图片预览区域 */
  .image-preview {
    width: 100%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--theme-surface-dark, #1a202c);
    border-radius: var(--border-radius, 4px);
    border: 1px solid var(--theme-border, rgba(255, 255, 255, 0.2));
    overflow: hidden;
    min-height: 150px;
  }

  /* 无图片状态 */
  .no-image-section {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-6, 1.5rem);
    margin-bottom: 16px;
  }

  .no-image-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    text-align: center;
  }

  .no-image-icon {
    font-size: var(--font-size-xl, 1.25rem);
    opacity: 0.5;
  }

  .no-image-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1, 0.25rem);
  }

  .no-image-title {
    font-size: var(--font-size-sm, 0.875rem);
    font-weight: 500;
    color: var(--theme-text, #ffffff);
  }

  .no-image-subtitle {
    font-size: var(--font-size-xs, 0.75rem);
    color: var(--theme-text-secondary, rgba(255, 255, 255, 0.8));
  }







  /* Canvas 预览样式 */
  .preview-canvas {
    max-width: 100%;
    max-height: 200px;
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid var(--border-color);
  }

  /* 图片操作按钮 */
  .image-actions {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }

  .change-image-button {
    padding: 6px 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .change-image-button:hover {
    background: var(--primary-color-dark);
  }

  /* 裁切相关样式 */
  .crop-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .grid-settings {
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
  }

  .grid-inputs {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
  }

  .grid-actions {
    display: flex;
    gap: 8px;
  }

  .crop-status {
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    font-size: 12px;
  }

  .status-info, .current-region {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
  }

  .status-label, .region-label {
    color: var(--text-secondary);
  }

  .status-value, .region-info {
    color: var(--text-primary);
    font-weight: 500;
  }



  .action-btn {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .action-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-hover);
  }

  .action-btn.secondary {
    background: var(--bg-secondary);
  }

  .action-btn.smart-crop {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
  }

  .action-btn.smart-crop:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }

  .action-btn.smart-crop:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
  }




  .region-info {
    text-align: center;
  }


  .grid-settings {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }


  .select-image-button {
    padding: 8px 16px;
    background: #4CAF50;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    cursor: pointer;
    font-size: 12px;
    margin-top: 8px;
    transition: background-color 0.2s;
  }

  .select-image-button:hover {
    background: #45a049;
  }

  .select-image-button:active {
    background: #3d8b40;
  }

  /* 智能裁切结果样式 */
  .smart-crop-results {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .error-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-radius: 4px;
    color: #f44336;
    font-size: 12px;
  }

  .error-icon {
    font-size: 14px;
  }

  .results-summary {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    font-size: 12px;
  }

  .summary-info {
    display: flex;
    justify-content: space-between;
  }

  .summary-label {
    color: var(--text-secondary);
  }

  .summary-value {
    color: var(--text-primary);
    font-weight: 500;
  }

  .regions-list-container {
    max-height: none;
    overflow: visible;
  }

  .elements-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 400px;
    overflow-y: auto;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
  }

  /* 隐藏滚动条但保持滚动功能 */
  .elements-list::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  .elements-list {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .element-item {
    width: 100%;
    padding: 8px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    display: block;
  }

  .element-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .element-item.selected {
    background: var(--primary-color-dark);
    border-color: var(--primary-color);
    box-shadow: 0 0 12px rgba(76, 175, 80, 0.4);
  }

  .element-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .element-preview {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 6px;
    overflow: hidden;
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
  }

  .preview-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .grid-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
  }

  .element-name-short {
    font-size: 12px;
    font-weight: bold;
    color: var(--text-primary);
    text-align: center;
    background: var(--bg-primary);
    padding: 4px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
  }

  .element-info {
    flex: 1;
    min-width: 0;
  }

  .element-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .element-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 13px;
  }

  .element-confidence {
    font-size: 11px;
    color: var(--primary-color);
    font-weight: 500;
  }

  .element-details {
    display: flex;
    gap: 12px;
    font-size: 11px;
    color: var(--text-secondary);
  }

  .element-size {
    font-weight: 500;
  }

  .element-position {
    opacity: 0.8;
  }

  .selected-indicator {
    margin-top: 8px;
    padding: 4px 8px;
    background: var(--primary-color);
    color: white;
    border-radius: 4px;
    font-size: 11px;
    text-align: center;
    font-weight: 600;
    display: inline-block;
  }

  .results-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
  }

  .summary-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .summary-label {
    font-size: 10px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .summary-value {
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 600;
  }

  .element-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
  }



  .element-name {
    flex: 1;
    color: var(--text-primary);
    font-weight: 500;
  }

  .element-confidence {
    color: var(--text-secondary);
    font-size: 10px;
  }

  .element-details {
    display: flex;
    justify-content: space-between;
    color: var(--text-secondary);
    font-size: 10px;
  }



  .no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    color: var(--text-secondary);
    font-size: 12px;
  }

  .no-results-icon {
    font-size: 24px;
    opacity: 0.5;
  }
</style>
