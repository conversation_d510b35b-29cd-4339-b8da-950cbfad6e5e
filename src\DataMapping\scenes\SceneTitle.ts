/**
 * Scene_Title 场景数据流定义
 */

import type { SceneDataFlow } from '../types';

/**
 * Scene_Title 场景的数据流
 */
export const SceneTitleDataFlow: SceneDataFlow = {
  sceneName: "Scene_Title",
  buttons: [
    {
      buttonName: "NewGame",
      triggerMethods: [
        "DataManager.setupNewGame()",
        "this._commandWindow.close()",
        "this.fadeOutAll()",
        "SceneManager.goto(Scene_Map)"
      ]
    },
    {
      buttonName: "Continue",
      triggerMethods: [
        "this._commandWindow.close()",
        "SceneManager.push(Scene_Load)"
      ]
    },
    {
      buttonName: "Options",
      triggerMethods: [
        "this._commandWindow.close()",
        "SceneManager.push(Scene_Options)"
      ]
    }
  ]
};
