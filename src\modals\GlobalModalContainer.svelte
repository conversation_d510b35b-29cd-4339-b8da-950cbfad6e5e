<script lang="ts">
  /**
   * 全局模态框容器
   * 集中管理所有模态框，避免重复创建和性能问题
   */
  import { modalReactive, ModalManager } from '../stores/modalStore.svelte';
  import UnifiedSelectionModal from './UnifiedSelectionModal.svelte';
  import ParentDataModal from './ParentDataModal.svelte';
  import CodeEditorModal from './codeModals/CodeEditorModal.svelte';

  // 响应式状态
  let dataBindingState = $derived(modalReactive.dataBinding);
  let parentDataState = $derived(modalReactive.parentData);

  // 数据绑定模态框的处理函数
  function handleDataBindingConfirm(result: string) {
    console.log('🔧 GlobalModalContainer: 数据绑定确认', result);
    ModalManager.handleDataBindingConfirm(result);
  }

  function handleDataBindingCancel() {
    console.log('🔧 GlobalModalContainer: 数据绑定取消');
    ModalManager.handleDataBindingCancel();
  }

  // 获取模态框属性
  let modalProps = $derived(ModalManager.getDataBindingProps());

  // 调试信息
  $effect(() => {
    if (dataBindingState.isOpen) {
      console.log('🔧 GlobalModalContainer: 数据绑定模态框已打开', modalProps);
    }
  });
</script>

<!-- 
  全局模态框容器
  只有在模态框真正需要显示时才渲染，避免性能影响
-->

<!-- 数据绑定模态框 -->
{#if dataBindingState.isOpen}
  <UnifiedSelectionModal
    bind:isOpen={dataBindingState.isOpen}
    onConfirm={handleDataBindingConfirm}
    mode={modalProps.mode || 'data'}
    initialPath={modalProps.initialPath || ''}
  />
{/if}

<!-- 🔑 父级数据模态框 -->
<ParentDataModal />

<!-- 🔑 代码编辑器模态框 -->
<CodeEditorModal />

<!--
  未来可以在这里添加其他类型的模态框
  例如：
  {#if confirmationState.isOpen}
    <ConfirmationModal ... />
  {/if}
-->

<style>
  /*
    全局模态框容器不需要样式
    所有样式都在具体的模态框组件中定义
  */
</style>
