<script lang="ts">
  import { getSceneDataFlow, getSupportedScenes } from './scenes';
  import type { SceneDataFlow } from './types';

  export let isOpen = false;
  export let sceneName = '';

  let selectedScene = sceneName || 'Scene_Title';
  let sceneData: SceneDataFlow | null = null;
  let supportedScenes = getSupportedScenes();

  // 拖拽相关变量
  let modalElement: HTMLDivElement;
  let isDragging = false;
  let dragOffset = { x: 0, y: 0 };
  let modalPosition = { x: 100, y: 100 };

  $: {
    if (isOpen && selectedScene) {
      sceneData = getSceneDataFlow(selectedScene);
    }
  }

  // 初始化选中场景
  $: if (isOpen && sceneName && sceneName !== selectedScene) {
    selectedScene = sceneName;
  }

  function closeModal() {
    isOpen = false;
  }

  function selectScene(scene: string) {
    selectedScene = scene;
    sceneData = getSceneDataFlow(scene);
  }

  // 拖拽功能
  function handleMouseDown(event: MouseEvent) {
    if (event.target === event.currentTarget || (event.target as HTMLElement).closest('.modal-header')) {
      isDragging = true;
      const rect = modalElement.getBoundingClientRect();
      dragOffset.x = event.clientX - rect.left;
      dragOffset.y = event.clientY - rect.top;

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      event.preventDefault();
    }
  }

  function handleMouseMove(event: MouseEvent) {
    if (isDragging) {
      modalPosition.x = event.clientX - dragOffset.x;
      modalPosition.y = event.clientY - dragOffset.y;

      // 限制在窗口范围内
      modalPosition.x = Math.max(0, Math.min(modalPosition.x, window.innerWidth - 800));
      modalPosition.y = Math.max(0, Math.min(modalPosition.y, window.innerHeight - 600));
    }
  }

  function handleMouseUp() {
    isDragging = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }
</script>

{#if isOpen}
  <!-- 模态框背景 -->
  <div class="modal-backdrop">
    <!-- 模态框内容 -->
    <div
      class="modal-content"
      bind:this={modalElement}
      style="left: {modalPosition.x}px; top: {modalPosition.y}px;"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
    >
      <!-- 可拖拽的标题栏 -->
      <div
        class="modal-header"
        on:mousedown={handleMouseDown}
        style="cursor: {isDragging ? 'grabbing' : 'grab'};"
        role="button"
        tabindex="0"
        aria-label="拖拽移动窗口"
      >
        <h2>📊 数据流分析</h2>
        <button class="close-btn" on:click={closeModal} aria-label="关闭">
          ✕
        </button>
      </div>

      <!-- 主要内容区域 -->
      <div class="modal-body">
        <!-- 左侧场景列表 -->
        <div class="scene-list">
          <h3>🎮 场景列表</h3>
          <div class="scene-items">
            {#each supportedScenes as scene}
              <button
                class="scene-item {selectedScene === scene ? 'active' : ''}"
                on:click={() => selectScene(scene)}
              >
                {scene}
              </button>
            {/each}
          </div>
        </div>

        <!-- 右侧详细信息 -->
        <div class="scene-details">
          {#if sceneData}
            <div class="scene-info">
              <h3>🎯 {sceneData.sceneName}</h3>

              {#if sceneData.buttons.length > 0}
                <div class="buttons-section">
                  <h4>🔘 按钮数据流:</h4>
                  {#each sceneData.buttons as button}
                    <div class="button-item">
                      <div class="button-name">• {button.buttonName}</div>
                      <div class="methods-list">
                        {#each button.triggerMethods as method}
                          <div class="method-item">{method}</div>
                        {/each}
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {:else}
            <div class="no-data">
              <p>❌ 场景 {selectedScene} 暂无数据流信息</p>
              <p>请选择其他场景查看</p>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
  }

  .modal-content {
    position: absolute;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    width: 800px;
    height: 600px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    color: var(--theme-text);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    border-bottom: 1px solid var(--theme-border);
    background: var(--theme-surface-elevated);
    user-select: none;
  }

  .modal-header h2 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--theme-text);
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--theme-text-secondary);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
  }

  .close-btn:hover {
    background-color: var(--theme-surface-hover);
    color: var(--theme-text);
  }

  .modal-body {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  /* 左侧场景列表 */
  .scene-list {
    width: 200px;
    background: var(--theme-surface-elevated);
    border-right: 1px solid var(--theme-border);
    padding: var(--spacing-3);
    overflow-y: auto;
  }

  .scene-list h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-md);
    color: var(--theme-text);
    font-weight: 600;
  }

  .scene-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .scene-item {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-2) var(--spacing-3);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--font-size-sm);
    color: var(--theme-text);
    text-align: left;
  }

  .scene-item:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-primary);
  }

  .scene-item.active {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
    border-color: var(--theme-primary);
  }

  /* 右侧详细信息 */
  .scene-details {
    flex: 1;
    padding: var(--spacing-4);
    overflow-y: auto;
  }

  .scene-info h3 {
    margin: 0 0 var(--spacing-4) 0;
    color: var(--theme-text);
    font-size: var(--font-size-lg);
    font-weight: 600;
  }

  .buttons-section {
    margin-top: var(--spacing-4);
  }

  .buttons-section h4 {
    margin: 0 0 var(--spacing-3) 0;
    color: var(--theme-text);
    font-size: var(--font-size-md);
    font-weight: 600;
  }

  .button-item {
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-3);
    background: var(--theme-surface-elevated);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--theme-primary);
  }

  .button-name {
    font-weight: 600;
    color: var(--theme-text);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-md);
  }

  .methods-list {
    margin-left: var(--spacing-4);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .method-item {
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    color: var(--theme-accent);
    background: var(--theme-surface);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--theme-border);
  }

  .no-data {
    text-align: center;
    color: var(--theme-text-secondary);
    padding: var(--spacing-8) var(--spacing-4);
  }

  .no-data p {
    margin: var(--spacing-2) 0;
    font-size: var(--font-size-md);
  }
</style>
