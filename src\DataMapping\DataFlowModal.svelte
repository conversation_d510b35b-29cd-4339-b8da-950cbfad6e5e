<script lang="ts">
  import { getSceneDataFlow } from './scenes';
  import type { SceneDataFlow, ButtonDataFlow } from './types';

  export let isOpen = false;
  export let sceneName = '';

  let sceneData: SceneDataFlow | null = null;

  $: {
    if (isOpen && sceneName) {
      sceneData = getSceneDataFlow(sceneName);
    }
  }

  function closeModal() {
    isOpen = false;
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  }
</script>

{#if isOpen}
  <!-- 模态框背景 -->
  <div 
    class="modal-backdrop" 
    on:click={handleBackdropClick}
    on:keydown={(e) => e.key === 'Escape' && closeModal()}
    role="dialog"
    aria-modal="true"
    tabindex="-1"
  >
    <!-- 模态框内容 -->
    <div class="modal-content">
      <!-- 标题栏 -->
      <div class="modal-header">
        <h2>📊 数据流分析 - {sceneName}</h2>
        <button class="close-btn" on:click={closeModal} aria-label="关闭">
          ✕
        </button>
      </div>

      <!-- 内容区域 -->
      <div class="modal-body">
        {#if sceneData}
          <div class="scene-info">
            <h3>🎮 场景: {sceneData.sceneName}</h3>
            
            {#if sceneData.buttons.length > 0}
              <div class="buttons-section">
                <h4>🔘 按钮数据流:</h4>
                {#each sceneData.buttons as button}
                  <div class="button-item">
                    <div class="button-name">• {button.buttonName}</div>
                    <div class="methods-list">
                      {#each button.triggerMethods as method}
                        <div class="method-item">{method}</div>
                      {/each}
                    </div>
                  </div>
                {/each}
              </div>
            {/if}
          </div>
        {:else}
          <div class="no-data">
            <p>❌ 场景 {sceneName} 暂无数据流信息</p>
            <p>支持的场景: Scene_Title</p>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    max-height: 80vh;
    width: 90%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f5f5f5;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .close-btn:hover {
    background-color: #e0e0e0;
  }

  .modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
  }

  .scene-info h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
  }

  .buttons-section {
    margin-top: 16px;
  }

  .buttons-section h4 {
    margin: 0 0 12px 0;
    color: #34495e;
    font-size: 14px;
  }

  .button-item {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #3498db;
  }

  .button-name {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
  }

  .methods-list {
    margin-left: 16px;
  }

  .method-item {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #e74c3c;
    background-color: #fff;
    padding: 4px 8px;
    margin: 2px 0;
    border-radius: 3px;
    border: 1px solid #ecf0f1;
  }

  .no-data {
    text-align: center;
    color: #7f8c8d;
    padding: 40px 20px;
  }

  .no-data p {
    margin: 8px 0;
  }
</style>
