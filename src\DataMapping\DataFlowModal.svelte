<script lang="ts">
  import { getSceneDataFlow, getSupportedScenes } from './scenes';
  import SceneItem from './components/SceneItem.svelte';
  import type { SceneDataFlow } from './types';

  interface Props {
    isOpen?: boolean;
    sceneName?: string;
  }

  let { isOpen = $bindable(false), sceneName = '' }: Props = $props();

  let selectedScene = $state(sceneName || 'Scene_Title');
  let supportedScenes = getSupportedScenes();



  // 拖拽相关变量
  let modalElement = $state<HTMLDivElement>();
  let isDragging = $state(false);
  let dragOffset = $state({ x: 0, y: 0 });
  let modalPosition = $state({ x: 100, y: 100 });

  // 使用derived来计算当前场景数据，避免无限循环
  let currentSceneData = $derived(
    isOpen && selectedScene ? getSceneDataFlow(selectedScene) : null
  );

  // 初始化选中场景
  $effect(() => {
    if (isOpen && sceneName && sceneName !== selectedScene) {
      selectedScene = sceneName;
    }
  });

  function closeModal() {
    isOpen = false;
  }

  function selectScene(scene: string) {
    selectedScene = scene;
    // currentSceneData 会通过 $derived 自动更新
  }

  // 拖拽功能
  function handleMouseDown(event: MouseEvent) {
    if (event.target === event.currentTarget || (event.target as HTMLElement).closest('.modal-header')) {
      isDragging = true;
      if (modalElement) {
        const rect = modalElement.getBoundingClientRect();
        dragOffset.x = event.clientX - rect.left;
        dragOffset.y = event.clientY - rect.top;

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        event.preventDefault();
      }
    }
  }

  function handleMouseMove(event: MouseEvent) {
    if (isDragging) {
      modalPosition.x = event.clientX - dragOffset.x;
      modalPosition.y = event.clientY - dragOffset.y;

      // 限制在窗口范围内
      modalPosition.x = Math.max(0, Math.min(modalPosition.x, window.innerWidth - 1000));
      modalPosition.y = Math.max(0, Math.min(modalPosition.y, window.innerHeight - 700));
    }
  }

  function handleMouseUp() {
    isDragging = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }
</script>

{#if isOpen}
  <!-- 模态框背景 -->
  <div class="modal-backdrop">
    <!-- 模态框内容 -->
    <div
      class="modal-content"
      bind:this={modalElement}
      style="left: {modalPosition.x}px; top: {modalPosition.y}px;"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
    >
      <!-- 可拖拽的标题栏 -->
      <div
        class="modal-header"
        onmousedown={handleMouseDown}
        style="cursor: {isDragging ? 'grabbing' : 'grab'};"
        role="button"
        tabindex="0"
        aria-label="拖拽移动窗口"
      >
        <div class="header-content">
          <h2>📊 数据流分析</h2>
          <div class="header-stats">
            <span class="stat-item">
              <span class="stat-icon">🎮</span>
              <span>{supportedScenes.length} 个场景</span>
            </span>
            <span class="stat-item">
              <span class="stat-icon">🔘</span>
              <span>{currentSceneData?.buttons.length || 0} 个按钮</span>
            </span>
          </div>
        </div>
        <button class="close-btn" onclick={closeModal} aria-label="关闭">
          ✕
        </button>
      </div>

      <!-- 主要内容区域 -->
      <div class="modal-body">
        <!-- 左侧导航 -->
        <div class="scene-nav">
          <h3>🎯 快速导航</h3>
          <div class="nav-items">
            {#each supportedScenes as scene}
              <button
                class="nav-item {selectedScene === scene ? 'active' : ''}"
                onclick={() => selectScene(scene)}
              >
                <span class="nav-icon">🎮</span>
                <span class="nav-text">{scene}</span>
                <span class="nav-badge">
                  {getSceneDataFlow(scene)?.buttons.length || 0}
                </span>
              </button>
            {/each}
          </div>
        </div>

        <!-- 右侧场景详情 -->
        <div class="scenes-container">
          {#if currentSceneData}
            {#key selectedScene}
              <SceneItem sceneData={currentSceneData} />
            {/key}
          {:else}
            <div class="no-data">
              <div class="no-data-icon">📭</div>
              <h3>暂无场景数据</h3>
              <p>场景 {selectedScene} 的数据流信息不存在</p>
              <p>请选择其他场景或添加数据流定义文件</p>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
  }

  .modal-content {
    position: absolute;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    width: 1000px;
    height: 700px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    color: var(--theme-text);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    border-bottom: 1px solid var(--theme-border);
    background: var(--theme-surface-elevated);
    user-select: none;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }

  .modal-header h2 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--theme-text);
    font-weight: 600;
  }

  .header-stats {
    display: flex;
    gap: var(--spacing-3);
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    background: var(--theme-surface);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
  }

  .stat-icon {
    font-size: var(--font-size-sm);
  }

  .close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--theme-text-secondary);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
  }

  .close-btn:hover {
    background-color: var(--theme-surface-hover);
    color: var(--theme-text);
  }

  .modal-body {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  /* 左侧导航 */
  .scene-nav {
    width: 220px;
    background: var(--theme-surface-elevated);
    border-right: 1px solid var(--theme-border);
    padding: var(--spacing-3);
    overflow-y: auto;
  }

  .scene-nav h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-md);
    color: var(--theme-text);
    font-weight: 600;
  }

  .nav-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .nav-item {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-2) var(--spacing-3);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--font-size-sm);
    color: var(--theme-text);
    text-align: left;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .nav-item:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-primary);
  }

  .nav-item.active {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
    border-color: var(--theme-primary);
  }

  .nav-icon {
    font-size: var(--font-size-sm);
  }

  .nav-text {
    flex: 1;
    font-weight: 500;
  }

  .nav-badge {
    background: var(--theme-accent-light);
    color: var(--theme-accent-dark);
    padding: 2px var(--spacing-1);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    min-width: 18px;
    text-align: center;
  }

  .nav-item.active .nav-badge {
    background: rgba(255, 255, 255, 0.2);
    color: var(--theme-text-inverse);
  }

  /* 右侧场景容器 */
  .scenes-container {
    flex: 1;
    padding: var(--spacing-3);
    overflow-y: auto;
    background: var(--theme-background);
  }

  /* 无数据状态 */
  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--theme-text-secondary);
    padding: var(--spacing-8);
  }

  .no-data-icon {
    font-size: 64px;
    margin-bottom: var(--spacing-4);
    opacity: 0.5;
  }

  .no-data h3 {
    margin: 0 0 var(--spacing-2) 0;
    font-size: var(--font-size-lg);
    color: var(--theme-text);
  }

  .no-data p {
    margin: var(--spacing-1) 0;
    font-size: var(--font-size-md);
    opacity: 0.8;
  }

  .no-data {
    text-align: center;
    color: var(--theme-text-secondary);
    padding: var(--spacing-8) var(--spacing-4);
  }

  .no-data p {
    margin: var(--spacing-2) 0;
    font-size: var(--font-size-md);
  }

  .debug-info {
    background: #f0f0f0;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
  }
</style>
