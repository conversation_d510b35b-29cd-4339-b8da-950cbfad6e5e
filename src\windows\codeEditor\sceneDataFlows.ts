/**
 * 场景数据流统一管理
 * 用于APISidebar展示场景相关的数据流代码
 */

export interface SceneDataFlowExample {
  name: string;
  description: string;
  code: string;
}

export interface SceneDataFlowGroup {
  sceneName: string;
  description: string;
  examples: SceneDataFlowExample[];
}

/**
 * 所有场景的数据流示例
 */
export const sceneDataFlows: SceneDataFlowGroup[] = [
  {
    sceneName: "Scene_Title",
    description: "标题场景数据流",
    examples: [
      {
        name: "数据资源访问",
        description: "获取标题场景使用的系统数据",
        code: `// 标题背景图片
const title1Name = $dataSystem.title1Name;
const title2Name = $dataSystem.title2Name;

// 游戏标题文本
const gameTitle = $dataSystem.gameTitle;

// 标题背景音乐
const titleBgm = $dataSystem.titleBgm;`
      },
      {
        name: "NewGame按钮数据流",
        description: "新游戏按钮触发的完整数据流程",
        code: `// 1. 初始化新游戏数据
DataManager.setupNewGame();

// 2. 淡出所有音频和画面
this.fadeOutAll();

// 3. 切换到地图场景
SceneManager.goto(Scene_Map);`
      },
      {
        name: "Continue按钮数据流",
        description: "继续游戏按钮的数据流程",
        code: `// 推入读取场景到栈顶
SceneManager.push(Scene_Load);`
      },
      {
        name: "Options按钮数据流",
        description: "选项按钮的数据流程",
        code: `// 推入选项场景到栈顶
SceneManager.push(Scene_Options);`
      }
    ]
  },
  {
    sceneName: "Scene_Options",
    description: "选项场景数据流",
    examples: [
      {
        name: "配置数据访问",
        description: "获取和设置游戏配置数据",
        code: `// 音量设置
const bgmVolume = ConfigManager.bgmVolume;
const bgsVolume = ConfigManager.bgsVolume;
const meVolume = ConfigManager.meVolume;
const seVolume = ConfigManager.seVolume;

// UI设置
const alwaysDash = ConfigManager.alwaysDash;
const commandRemember = ConfigManager.commandRemember;
const touchUI = ConfigManager.touchUI;`
      },
      {
        name: "Cancel按钮数据流",
        description: "取消按钮触发的数据流程",
        code: `// 1. 保存配置数据到本地
ConfigManager.save();

// 2. 返回上一个场景
this.popScene();`
      },
      {
        name: "布局计算",
        description: "选项窗口的布局计算",
        code: `// 获取画面尺寸
const boxWidth = Graphics.boxWidth;
const boxHeight = Graphics.boxHeight;

// 计算窗口居中位置
const windowWidth = 400;
const windowHeight = this.calcWindowHeight(7, true);
const x = (boxWidth - windowWidth) / 2;
const y = (boxHeight - windowHeight) / 2;`
      }
    ]
  },
  {
    sceneName: "Scene_Map",
    description: "地图场景数据流",
    examples: [
      {
        name: "Menu按钮数据流",
        description: "菜单按钮的数据流程",
        code: `// 推入菜单场景到栈顶
SceneManager.push(Scene_Menu);`
      },
      {
        name: "Save按钮数据流",
        description: "保存按钮的数据流程",
        code: `// 推入保存场景到栈顶
SceneManager.push(Scene_Save);`
      },
      {
        name: "Load按钮数据流",
        description: "读取按钮的数据流程",
        code: `// 推入读取场景到栈顶
SceneManager.push(Scene_Load);`
      },
      {
        name: "Transfer按钮数据流",
        description: "场所移动按钮的数据流程",
        code: `// 执行场所移动
$gamePlayer.reserveTransfer(mapId, x, y, direction, fadeType);`
      }
    ]
  },
  {
    sceneName: "Scene_Menu",
    description: "菜单场景数据流",
    examples: [
      {
        name: "Item按钮数据流",
        description: "物品按钮的数据流程",
        code: `// 推入物品场景到栈顶
SceneManager.push(Scene_Item);`
      },
      {
        name: "Skill按钮数据流",
        description: "技能按钮的数据流程",
        code: `// 推入技能场景到栈顶
SceneManager.push(Scene_Skill);`
      },
      {
        name: "Equip按钮数据流",
        description: "装备按钮的数据流程",
        code: `// 推入装备场景到栈顶
SceneManager.push(Scene_Equip);`
      },
      {
        name: "Status按钮数据流",
        description: "状态按钮的数据流程",
        code: `// 推入状态场景到栈顶
SceneManager.push(Scene_Status);`
      },
      {
        name: "Formation按钮数据流",
        description: "队伍编成按钮的数据流程",
        code: `// 推入队伍编成场景到栈顶
SceneManager.push(Scene_Formation);`
      },
      {
        name: "Options按钮数据流",
        description: "选项按钮的数据流程",
        code: `// 推入选项场景到栈顶
SceneManager.push(Scene_Options);`
      },
      {
        name: "Save按钮数据流",
        description: "保存按钮的数据流程",
        code: `// 推入保存场景到栈顶
SceneManager.push(Scene_Save);`
      },
      {
        name: "Game End按钮数据流",
        description: "游戏结束按钮的数据流程",
        code: `// 推入游戏结束场景到栈顶
SceneManager.push(Scene_GameEnd);`
      }
    ]
  }
];

/**
 * 根据场景名称获取数据流示例
 */
export function getSceneDataFlowExamples(sceneName: string): SceneDataFlowGroup | null {
  return sceneDataFlows.find(scene => scene.sceneName === sceneName) || null;
}

/**
 * 获取所有支持的场景名称
 */
export function getSupportedSceneNames(): string[] {
  return sceneDataFlows.map(scene => scene.sceneName);
}
