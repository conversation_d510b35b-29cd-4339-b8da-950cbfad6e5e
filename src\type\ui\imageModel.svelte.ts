import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * 脚本接口定义
 */
interface Script {
    id: string;
    name: string;
    type: 'lifecycle' | 'interaction' | 'custom';
    enabled: boolean;
    code: string;
    description: string;
}

// 裁切区域类型定义
export interface CropRegion {
    id: string;
    label: string;
    sx: number;  // 源图片裁切X
    sy: number;  // 源图片裁切Y
    sw: number;  // 源图片裁切宽度
    sh: number;  // 源图片裁切高度
    gridIndex: number;  // 在网格中的索引
}

export class ImageModel extends BaseObjectModel {

    constructor(image: any) {
        super(image);

        // 🔑 统一的脚本系统 - 从原始对象获取脚本数组
        this.componentScripts = image.componentScripts || [];

        // 🔑 如果没有脚本，添加默认脚本
        if (this.componentScripts.length === 0) {
            this.componentScripts = [{
                id: 'default_lifecycle',
                name: '生命周期脚本',
                type: 'lifecycle',
                enabled: true,
                description: '默认的生命周期脚本',
                code: `/**
 * 🚀 onStart - 对象启动生命周期
 * 触发时机: 对象创建并添加到父容器时自动触发
 * 作用: 初始化对象状态、设置默认值、绑定数据等
 * 配合方法: 无需手动调用，系统自动触发
 */
function onStart() {
  console.log("对象启动:", self.name || "unnamed");
  // 在这里添加初始化逻辑
  // 例如: 设置初始图片、绑定数据源、初始化变量等
}

/**
 * 🔄 onUpdate - 每帧更新生命周期
 * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用
 * 作用: 实现动画、实时数据更新、状态检查等
 * 配合方法: 无需手动调用，系统自动触发
 * 注意: 避免在此方法中执行耗时操作
 */
// function onUpdate() {
//   console.log("每帧更新:", self.name);
//   // 在这里添加每帧更新逻辑
//   // 例如: 动画更新、实时数据显示、状态检查等
// }

/**
 * 📝 onFieldUpdate - 字段更新生命周期
 * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发
 * 作用: 响应数据变化、更新UI显示、同步状态等
 * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)
 * 参数: ctx - 包含字段更新上下文信息的对象
 */
// function onFieldUpdate(ctx) {
//   console.log("字段更新:", self.name, ctx);
//   // 在这里添加字段更新时的处理逻辑
//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }
//   // 例如: 根据 ctx.field 判断更新哪个属性
// }

/**
 * 🗑️ onDestroy - 对象销毁生命周期
 * 触发时机: 对象从父容器移除时自动触发
 * 作用: 清理资源、保存数据、解除绑定等
 * 配合方法: 无需手动调用，系统自动触发
 */
// function onDestroy() {
//   console.log("对象销毁:", self.name);
//   // 在这里添加清理逻辑
//   // 例如: 清理定时器、保存状态、解除事件绑定等
// }`
            }];
        }

        console.log('🔧 ImageModel: 脚本系统初始化', {
            hasOriginalScripts: !!image.componentScripts,
            scriptsCount: this.componentScripts.length,
            scriptNames: this.componentScripts.map(s => s.name)
        });

        // 初始化图片特有属性
        this.width = image.width || 100;
        this.height = image.height || 100;
        // 🔑 支持 src 属性，兼容 imagePath
        this.imagePath = image.src || image.imagePath || '';
        this.scaleMode = image.scaleMode || 'none';
        this.preserveAspectRatio = image.preserveAspectRatio !== false;
        this.regions = image.regions || [];
        this.currentRegionIndex = image.currentRegionIndex || 0;
        this.gridRows = image.gridRows || 1;
        this.gridCols = image.gridCols || 1;

        // 初始化智能裁切属性
        this.detectedElements = image.detectedElements || [];
        this.showSmartCropResults = image.showSmartCropResults || false;

        console.log('🔧 ImageModel: 创建图片模型', image);

        // setupSync() 已经在基类构造函数中调用了
    }



    // 🔑 统一的脚本系统
    componentScripts = $state<Script[]>([]);   // 脚本数组

    // 图片路径属性
    imagePath = $state('');             // 项目资源路径

    // 显示属性
    scaleMode = $state('none');         // 缩放模式: stretch, fit, fill, none
    preserveAspectRatio = $state(true); // 保持宽高比

    // 裁切相关属性
    regions = $state<CropRegion[]>([]);
    currentRegionIndex = $state(0);
    gridRows = $state(1);
    gridCols = $state(1);

    // 智能裁切相关属性
    detectedElements = $state<any[]>([]);
    showSmartCropResults = $state(false);

    // 用于触发属性面板重新渲染的计数器
    private _updateCounter = $state(0);

    // 计算属性：是否为多区域裁切
    get isMultiRegion(): boolean {
        return this.regions.length > 1;
    }

    // 计算属性：当前区域
    get currentRegion(): CropRegion | null {
        return this.regions[this.currentRegionIndex] || null;
    }

    // 获取更新计数器（用于触发属性面板重新渲染）
    get updateCounter(): number {
        return this._updateCounter;
    }

    // 触发更新
    private _triggerUpdate(): void {
        this._updateCounter++;
        console.log('🔧 ImageModel: 触发更新，计数器:', this._updateCounter);
    }

    /**
     * 设置Image特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Image特有的属性
     */
    protected setupSpecificSync(): void {
        // 🔑 同步组件脚本数组
        if (this._originalObject.componentScripts !== this.componentScripts) {
            this._originalObject.componentScripts = this.componentScripts;
            console.log('🔧 ImageModel: 同步组件脚本', this.componentScripts);
        }

        // 设置加载完成回调
        if (this._originalObject.setOnLoadCallback && typeof this._originalObject.setOnLoadCallback === 'function') {
            this._originalObject.setOnLoadCallback((width: number, height: number) => {
                console.log('🔧 ImageModel: 图片加载完成回调，更新尺寸:', width, 'x', height);
                this.width = width;
                this.height = height;
                // 同步区域数据
                this.syncRegionsFromOriginal();

                // 🔧 关键修复：触发属性面板重新检查bitmap状态
                // 通过修改一个响应式属性来触发重新渲染
                this._triggerUpdate();
            });
        }

        // 🔑 同步图片路径（使用 src 属性）
        if (this._originalObject.src !== this.imagePath) {
            console.log('🔧 ImageModel: 图片路径变化', {
                old: this._originalObject.src,
                new: this.imagePath,
                hasSrcProperty: 'src' in this._originalObject
            });

            // 使用 src 属性（推荐）
            this._originalObject.src = this.imagePath;
        }

        // 同步显示属性
        if (this._originalObject.scaleMode !== this.scaleMode) {
            this._originalObject.scaleMode = this.scaleMode;
            if (this._originalObject.setScaleMode && typeof this._originalObject.setScaleMode === 'function') {
                this._originalObject.setScaleMode(this.scaleMode);
            }
        }

        this._originalObject.preserveAspectRatio = this.preserveAspectRatio;

        // 同步裁切相关属性
        this._originalObject.gridRows = this.gridRows;
        this._originalObject.gridCols = this.gridCols;

        // 同步智能裁切相关属性
        this._originalObject.detectedElements = this.detectedElements;
        this._originalObject.showSmartCropResults = this.showSmartCropResults;

        // 初始同步区域数据
        this.syncRegionsFromOriginal();

    }

    /**
     * 设置图片路径
     */
    public setImagePath(path: string): void {
        this.imagePath = path;
    }

    /**
     * 设置缩放模式
     */
    public setScaleMode(mode: 'stretch' | 'fit' | 'fill' | 'none'): void {
        this.scaleMode = mode;
    }

    /**
     * 设置图片尺寸
     */
    public setImageSize(width: number, height: number): void {
        this.width = width;
        this.height = height;
    }

    /**
     * 生成网格区域
     */
    public generateGridRegions(): void {
        if (this._originalObject && typeof this._originalObject.generateGridRegions === 'function') {
            const regions = this._originalObject.generateGridRegions(this.gridRows, this.gridCols);
            this.syncRegionsFromOriginal();

            // 生成网格后，同步第一个区域的尺寸
            if (this.isMultiRegion && this.currentRegion) {
                console.log('🔄 ImageModel: 生成网格后同步尺寸', {
                    regionIndex: this.currentRegionIndex,
                    region: this.currentRegion,
                    newSize: `${this.currentRegion.sw}x${this.currentRegion.sh}`
                });

                this.width = this.currentRegion.sw;
                this.height = this.currentRegion.sh;
            }
        }
    }

    /**
     * 设置当前区域
     */
    public setCurrentRegion(index: number): void {
        if (this._originalObject && typeof this._originalObject.setCurrentRegion === 'function') {
            if (this._originalObject.setCurrentRegion(index)) {
                this.currentRegionIndex = index;

                // 同步当前区域的尺寸到模型
                if (this.isMultiRegion && this.currentRegion) {
                    console.log('🎯 ImageModel: 设置区域后同步尺寸', {
                        regionIndex: this.currentRegionIndex,
                        region: this.currentRegion,
                        newSize: `${this.currentRegion.sw}x${this.currentRegion.sh}`
                    });

                    this.width = this.currentRegion.sw;
                    this.height = this.currentRegion.sh;
                }
            }
        }
    }

    /**
     * 重置为默认区域（整张图片）
     */
    public resetToDefaultRegion(): void {
        if (this._originalObject && typeof this._originalObject.resetToDefaultRegion === 'function') {
            this._originalObject.resetToDefaultRegion();

            // 重置网格设置
            this.gridRows = 1;
            this.gridCols = 1;

            // 同步区域数据
            this.syncRegionsFromOriginal();

            // 重置宽高为原始图片尺寸
            if (this._originalObject.bitmap) {
                this.width = this._originalObject.bitmap.width;
                this.height = this._originalObject.bitmap.height;

                console.log('🔄 ImageModel: 重置宽高为原始尺寸', {
                    width: this.width,
                    height: this.height
                });
            }

            console.log('🔄 ImageModel: 重置为默认区域', {
                regions: this.regions.length,
                currentIndex: this.currentRegionIndex,
                gridSize: `${this.gridRows}x${this.gridCols}`,
                size: `${this.width}x${this.height}`
            });
        }
    }

    /**
     * 从原始对象同步区域数据
     */
    private syncRegionsFromOriginal(): void {
        console.log('🔄 ImageModel: syncRegionsFromOriginal 调用', {
            hasOriginalObject: !!this._originalObject,
            originalObjectRegions: this._originalObject?.regions?.length || 0,
            currentModelRegions: this.regions.length
        });

        if (this._originalObject?.regions) {
            this.regions = [...this._originalObject.regions];
            this.currentRegionIndex = this._originalObject.currentRegionIndex || 0;
            console.log('🔄 ImageModel: 同步regions成功', {
                regionsCount: this.regions.length,
                currentIndex: this.currentRegionIndex
            });
        } else {
            console.log('🔄 ImageModel: 原始对象没有regions数据，保持当前状态');
        }

        // 同步智能裁切数据
        if (this._originalObject?.detectedElements) {
            this.detectedElements = [...this._originalObject.detectedElements];
            this.showSmartCropResults = this._originalObject.showSmartCropResults || false;
            console.log('🔄 ImageModel: 同步智能裁切数据成功', {
                elementsCount: this.detectedElements.length,
                showResults: this.showSmartCropResults
            });
        } else {
            // 如果没有智能裁切数据，重置状态
            this.detectedElements = [];
            this.showSmartCropResults = false;
            console.log('🔄 ImageModel: 重置智能裁切状态');
        }
    }

    /**
     * 获取图片信息
     */
    public getImageInfo(): {
        hasImage: boolean;
        isFromPath: boolean;
        source: string;
        size: { width: number; height: number };
        scaleMode: string;
    } {
        const hasPath = this.imagePath && this.imagePath.trim() !== '';

        return {
            hasImage: true, // 总是有图片（要么是用户指定的，要么是默认的）
            isFromPath: !!hasPath,
            source: this.imagePath || 'ui/defaultImage.png', // 没有路径时使用默认图片
            size: { width: this.width, height: this.height },
            scaleMode: this.scaleMode
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建UIImage对象（在构造函数中传入宽高，避免异步加载问题）
        codes.push(`${indent}const ${varName} = new UIImage({`);

        // 传入目标尺寸
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);

        if (this.imagePath) {
            codes.push(`${indent}    src: '${this.imagePath}',`);
        }

        // 只有在多区域时才生成裁切数据
        if (this.isMultiRegion) {
            codes.push(`${indent}    regions: ${JSON.stringify(this.regions)},`);
            codes.push(`${indent}    currentRegionIndex: ${this.currentRegionIndex},`);
            codes.push(`${indent}    gridRows: ${this.gridRows},`);
            codes.push(`${indent}    gridCols: ${this.gridCols},`);
        }

        // 🔑 生成组件脚本数组（如果有的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            codes.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, `\n${indent}`)},`);
        }

        codes.push(`${indent}    scaleMode: 'none',`);
        codes.push(`${indent}    preserveAspectRatio: ${this.preserveAspectRatio}`);
        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 🔑 检查是否有非空的脚本内容
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

    /**
     * 克隆当前图片对象 - 调用插件的 clone 方法
     */
    clone(): ImageModel {
        console.log('🔄 ImageModel: 开始克隆图片对象（调用插件方法）');

        // 1. 调用原始 UIImage 对象的 clone 方法
        const originalUIImage = this.getOriginalObject();
        if (!originalUIImage || typeof originalUIImage.clone !== 'function') {
            console.error('❌ ImageModel: 原始对象没有 clone 方法');
            throw new Error('UIImage 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIImage = originalUIImage.clone({
            offsetPosition: false,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 ImageModel 包装克隆的对象
        const clonedModel = new ImageModel(clonedUIImage);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ ImageModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIImage 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIImage 特有属性
            src: this.imagePath,
            scaleMode: this.scaleMode,
            preserveAspectRatio: this.preserveAspectRatio,
            regions: JSON.parse(JSON.stringify(this.regions)), // 深拷贝区域数据
            currentRegionIndex: this.currentRegionIndex,
            gridRows: this.gridRows,
            gridCols: this.gridCols,

            // 🔑 克隆脚本数组
            componentScripts: this.componentScripts ?
                this.componentScripts.map(script => ({ ...script })) : []
        };
    }

}

// 注册ImageModel到基类容器
BaseObjectModel.registerModel('UIImage', ImageModel);
BaseObjectModel.registerModel('Image', ImageModel);
