import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * 脚本接口定义
 */
interface Script {
    id: string;
    name: string;
    type: 'lifecycle' | 'interaction' | 'custom';
    enabled: boolean;
    code: string;
    description: string;
}

/**
 * UIButton模型类 - 使用绑定机制
 */
export class ButtonModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UIButton';

    // 🔑 子组件绑定属性（所有组件都由外部绑定）- 存储模型对象
    boundDefaultSprite = $state<BaseObjectModel | null>(null);     // 绑定的默认状态精灵模型 (UIImage)
    boundHoverSprite = $state<BaseObjectModel | null>(null);       // 绑定的悬停状态精灵模型 (UIImage)
    boundPressedSprite = $state<BaseObjectModel | null>(null);     // 绑定的按下状态精灵模型 (UIImage)

    // 状态配置
    enabled = $state(true);





    // 🔑 统一的脚本系统
    componentScripts = $state<Script[]>([]);   // 脚本数组



    // 🔑 事件执行控制（用于编辑器调试）
    executeEventsInEditor = $state(false);

    constructor(button: any = {}) {
        super(button);

        // 初始化绑定属性
        this.boundDefaultSprite = button.boundDefaultSprite || null;
        this.boundHoverSprite = button.boundHoverSprite || null;
        this.boundPressedSprite = button.boundPressedSprite || null;

        // 初始化状态
        this.enabled = button.enabled !== false;

        // 初始化事件执行控制
        this.executeEventsInEditor = button.executeEventsInEditor || false;





        // 🔑 初始化脚本系统
        this.componentScripts = button.componentScripts || [
            {
                id: 'default_script',
                name: '默认脚本',
                enabled: true,
                code: `/**
 * 🚀 onStart - 对象启动生命周期
 * 触发时机: 对象创建并添加到父容器时自动触发
 * 作用: 初始化对象状态、设置默认值、绑定数据等
 * 配合方法: 无需手动调用，系统自动触发
 */
function onStart() {
  console.log("对象启动:", self.name || "unnamed");
  // 在这里添加初始化逻辑
  // 例如: 设置初始文本、绑定数据源、初始化变量等
}

/**
 * 🔄 onUpdate - 每帧更新生命周期
 * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用
 * 作用: 实现动画、实时数据更新、状态检查等
 * 配合方法: 无需手动调用，系统自动触发
 * 注意: 避免在此方法中执行耗时操作
 */
// function onUpdate() {
//   console.log("每帧更新:", self.name);
//   // 在这里添加每帧更新逻辑
//   // 例如: 动画更新、实时数据显示、状态检查等
// }

/**
 * 📝 onFieldUpdate - 字段更新生命周期
 * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发
 * 作用: 响应数据变化、更新UI显示、同步状态等
 * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)
 * 参数: ctx - 包含字段更新上下文信息的对象
 */
// function onFieldUpdate(ctx) {
//   console.log("字段更新:", self.name, ctx);
//   // 在这里添加字段更新时的处理逻辑
//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }
//   // 例如: 根据 ctx.field 判断更新哪个属性
// }

/**
 * 🗑️ onDestroy - 对象销毁生命周期
 * 触发时机: 对象从父容器移除时自动触发
 * 作用: 清理资源、保存数据、解除绑定等
 * 配合方法: 无需手动调用，系统自动触发
 */
// function onDestroy() {
//   console.log("对象销毁:", self.name);
//   // 在这里添加清理逻辑
//   // 例如: 清理定时器、保存状态、解除事件绑定等
// }

/**
 * 👆 onClick - 单击交互事件
 * 触发时机: 用户单击对象时触发
 * 作用: 处理点击逻辑、切换状态、执行操作等
 * 配合方法: 无需手动调用，用户交互自动触发
 */
// function onClick() {
//   console.log("对象被点击:", self.name);
//   // 在这里添加点击逻辑
//   // 例如: 切换状态、打开菜单、执行命令等
// }

/**
 * 👆👆 onDoubleClick - 双击交互事件
 * 触发时机: 用户双击对象时触发
 * 作用: 处理双击特殊逻辑、快捷操作等
 * 配合方法: 无需手动调用，用户交互自动触发
 */
// function onDoubleClick() {
//   console.log("对象被双击:", self.name);
//   // 在这里添加双击逻辑
//   // 例如: 快速编辑、全屏显示、快捷操作等
// }

/**
 * 🖱️ onHover - 鼠标悬停事件
 * 触发时机: 鼠标进入对象区域时触发
 * 作用: 显示提示信息、改变外观、预览效果等
 * 配合方法: 通常与 onHoverOut 配对使用
 */
// function onHover() {
//   console.log("鼠标悬停:", self.name);
//   // 在这里添加悬停逻辑
//   // 例如: 显示工具提示、改变颜色、显示预览等
// }

/**
 * 🖱️ onHoverOut - 鼠标离开事件
 * 触发时机: 鼠标离开对象区域时触发
 * 作用: 隐藏提示信息、恢复外观、清理预览等
 * 配合方法: 通常与 onHover 配对使用
 */
// function onHoverOut() {
//   console.log("鼠标离开:", self.name);
//   // 在这里添加鼠标离开逻辑
//   // 例如: 隐藏工具提示、恢复颜色、清理预览等
// }

/**
 * ⬇️ onPress - 按下事件
 * 触发时机: 鼠标按下（但未释放）时触发
 * 作用: 开始拖拽、显示按下效果、记录按下状态等
 * 配合方法: 通常与 onRelease 配对使用
 */
// function onPress() {
//   console.log("对象按下:", self.name);
//   // 在这里添加按下逻辑
//   // 例如: 开始拖拽、改变外观、记录状态等
// }

/**
 * ⬆️ onRelease - 释放事件
 * 触发时机: 鼠标释放时触发
 * 作用: 结束拖拽、恢复外观、完成操作等
 * 配合方法: 通常与 onPress 配对使用
 */
// function onRelease() {
//   console.log("对象释放:", self.name);
//   // 在这里添加释放逻辑
//   // 例如: 结束拖拽、恢复外观、完成操作等
// }`,
                description: '默认脚本，包含常用方法模板'
            }
        ];



        console.log('🔘 ButtonModel: 创建按钮模型', this);

        // 🔑 延迟重建绑定关系（等待所有子对象加载完成）
        setTimeout(() => this.rebuildBindingsFromChildren(), 0);
    }



    /**
     * 🔑 检查是否有非空的脚本内容
     */
    private _hasNonEmptyScripts(): boolean {
        if (!this.componentScripts || this.componentScripts.length === 0) return false;

        // 检查是否有启用且有代码的脚本
        return this.componentScripts.some(script =>
            script.enabled && script.code && script.code.trim().length > 0
        );
    }

    /**
     * 🔑 从子对象重建绑定关系（用于加载保存的场景）
     * 根据子对象的类型和数量自动识别并重建绑定关系
     */
    rebuildBindingsFromChildren(): void {
        console.log('🔄 ButtonModel: 开始重建绑定关系', {
            childrenCount: this.children.length,
            children: this.children.map(child => ({
                className: child.className,
                name: child.name
            }))
        });

        // 清空现有绑定
        this.boundDefaultSprite = null;
        this.boundHoverSprite = null;
        this.boundPressedSprite = null;

        // 遍历子对象，根据顺序重建绑定（按照常见的绑定顺序）
        const uiImages = this.children.filter(child => child.className === 'UIImage');

        if (uiImages.length >= 1) {
            this.boundDefaultSprite = uiImages[0];
            console.log('🔗 重建绑定: 默认状态精灵', uiImages[0].name);
        }

        if (uiImages.length >= 2) {
            this.boundHoverSprite = uiImages[1];
            console.log('🔗 重建绑定: 悬停状态精灵', uiImages[1].name);
        }

        if (uiImages.length >= 3) {
            this.boundPressedSprite = uiImages[2];
            console.log('🔗 重建绑定: 按下状态精灵', uiImages[2].name);
        }

        console.log('✅ ButtonModel: 绑定关系重建完成', {
            boundDefaultSprite: this.boundDefaultSprite?.name || 'null',
            boundHoverSprite: this.boundHoverSprite?.name || 'null',
            boundPressedSprite: this.boundPressedSprite?.name || 'null'
        });
    }

    /**
     * 设置Button特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Button特有的属性
     */
    protected setupSpecificSync(): void {
        try {
            // 🔧 绑定默认状态精灵：从模型对象获取原始对象
            if (this.boundDefaultSprite) {
                // 🔧 安全检查：确保绑定对象有getOriginalObject方法
                if (typeof this.boundDefaultSprite.getOriginalObject === 'function') {
                    const originalDefaultSprite = this.boundDefaultSprite.getOriginalObject();
                    if (this._originalObject.bindDefaultSprite) {
                        this._originalObject.bindDefaultSprite(originalDefaultSprite);
                    } else {
                        this._originalObject.boundDefaultSprite = originalDefaultSprite;
                    }
                } else {
                    console.warn('🚨 ButtonModel: boundDefaultSprite 不是有效的模型对象，跳过绑定');
                    this._originalObject.boundDefaultSprite = null;
                }
            } else {
                this._originalObject.boundDefaultSprite = null;
            }

            // 🔧 绑定悬停状态精灵：从模型对象获取原始对象
            if (this.boundHoverSprite) {
                // 🔧 安全检查：确保绑定对象有getOriginalObject方法
                if (typeof this.boundHoverSprite.getOriginalObject === 'function') {
                    const originalHoverSprite = this.boundHoverSprite.getOriginalObject();
                    if (this._originalObject.bindHoverSprite) {
                        this._originalObject.bindHoverSprite(originalHoverSprite);
                    } else {
                        this._originalObject.boundHoverSprite = originalHoverSprite;
                    }
                } else {
                    console.warn('🚨 ButtonModel: boundHoverSprite 不是有效的模型对象，跳过绑定');
                    this._originalObject.boundHoverSprite = null;
                }
            } else {
                this._originalObject.boundHoverSprite = null;
            }

            // 🔧 绑定按下状态精灵：从模型对象获取原始对象
            if (this.boundPressedSprite) {
                // 🔧 安全检查：确保绑定对象有getOriginalObject方法
                if (typeof this.boundPressedSprite.getOriginalObject === 'function') {
                    const originalPressedSprite = this.boundPressedSprite.getOriginalObject();
                    if (this._originalObject.bindPressedSprite) {
                        this._originalObject.bindPressedSprite(originalPressedSprite);
                    } else {
                        this._originalObject.boundPressedSprite = originalPressedSprite;
                    }
                } else {
                    console.warn('🚨 ButtonModel: boundPressedSprite 不是有效的模型对象，跳过绑定');
                    this._originalObject.boundPressedSprite = null;
                }
            } else {
                this._originalObject.boundPressedSprite = null;
            }

            // 同步状态
            if (this._originalObject.setEnabled && typeof this._originalObject.setEnabled === 'function') {
                this._originalObject.setEnabled(this.enabled);
            } else {
                this._originalObject.enabled = this.enabled;
            }

            // 🔑 同步 UIComponent 属性
            this._originalObject.componentScripts = this.componentScripts;

            // 🔑 同步事件执行控制
            this._originalObject.executeEventsInEditor = this.executeEventsInEditor;



        } catch (error) {
            console.error('ButtonModel: 同步失败', error);
        }
    }

    /**
     * 获取按钮信息用于显示
     */
    getButtonInfo(): {
        bindings: {
            boundDefaultSprite: any;
            boundHoverSprite: any;
            boundPressedSprite: any;
        };
        enabled: boolean;
        events: {
            onClickCode: string;
            onHoverCode: string;
            onHoverOutCode: string;
            onPressCode: string;
            onReleaseCode: string;
            onDoubleClickCode: string;
        };
        size: { width: number; height: number };
    } {
        return {
            bindings: {
                boundDefaultSprite: this.boundDefaultSprite,
                boundHoverSprite: this.boundHoverSprite,
                boundPressedSprite: this.boundPressedSprite
            },
            enabled: this.enabled,
            // 🔑 UIComponent 属性
            componentScripts: this.componentScripts,
            size: {
                width: this.width,
                height: this.height
            }
        };
    }



    /**
     * 序列化为JSON
     */
    toJSON(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            alpha: this.alpha,
            visible: this.visible,
            rotation: this.rotation,
            zIndex: this.zIndex,
            scaleX: this.scaleX,
            scaleY: this.scaleY,
            // Button特有属性
            componentType: this.componentType,
            boundDefaultSprite: this.boundDefaultSprite,
            boundHoverSprite: this.boundHoverSprite,
            boundPressedSprite: this.boundPressedSprite,
            enabled: this.enabled,


            // 🔑 生成组件脚本（如果有的话）
            ...(this._hasNonEmptyScripts() ? {
                componentScripts: this.componentScripts
            } : {}),


        };
    }

    /**
     * 从JSON反序列化
     */
    static fromJSON(data: any): ButtonModel {
        return new ButtonModel(data);
    }

    /**
     * 克隆方法（实现抽象方法）- 调用插件的 clone 方法
     */
    clone(): ButtonModel {
        console.log('🔄 ButtonModel: 开始克隆Button对象（调用插件方法）');

        // 1. 调用原始 UIButton 对象的 clone 方法
        const originalUIButton = this.getOriginalObject();
        if (!originalUIButton || typeof originalUIButton.clone !== 'function') {
            console.error('❌ ButtonModel: 原始对象没有 clone 方法');
            throw new Error('UIButton 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIButton = originalUIButton.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 ButtonModel 包装克隆的对象
        const clonedModel = new ButtonModel(clonedUIButton);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ ButtonModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }





    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const lines: string[] = [];

        // 生成构造函数调用
        lines.push(`${indent}const ${varName} = new UIButton({`);
        lines.push(`${indent}    x: ${this.x},`);
        lines.push(`${indent}    y: ${this.y},`);
        lines.push(`${indent}    width: ${this.width},`);
        lines.push(`${indent}    height: ${this.height},`);
        lines.push(`${indent}    enabled: ${this.enabled},`);

        // 🔑 生成脚本数组（如果有非空脚本的话）
        if (this.componentScripts && this._hasNonEmptyScripts()) {
            lines.push(`${indent}    componentScripts: ${JSON.stringify(this.componentScripts, null, 8).replace(/\n/g, '\n' + indent)},`);
        }



        lines.push(`${indent}});`);

        return lines.join('\n');
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns Button特定属性设置代码
     */
    protected generateSpecificProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 不在这里生成绑定代码，因为子对象还没创建
        // 绑定代码将在 generateBindingCode() 中生成

        // 🔑 按钮事件和组件脚本现在在构造函数中生成，这里不需要额外设置

        return codes.join('\n');
    }

    /**
     * 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成绑定代码（在子对象创建之后调用）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 绑定代码
     */
    protected generateBindingCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 辅助函数：查找绑定对象对应的子对象变量名
        const findChildVarName = (boundObject: any): string | null => {
            if (!boundObject) return null;

            // 🔑 现在boundObject是模型对象，需要直接比较模型对象
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child === boundObject) {
                    return `${varName}_child${i}`;
                }
            }
            return null;
        };

        // 检查是否有任何绑定，如果有则添加注释
        if (this.boundDefaultSprite || this.boundHoverSprite || this.boundPressedSprite) {
            codes.push(`${indent}// 绑定子组件`);
        }

        // 绑定子组件（如果有的话）
        if (this.boundDefaultSprite) {
            const childVarName = findChildVarName(this.boundDefaultSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定默认状态精灵 (UIImage)`);
                codes.push(`${indent}${varName}.bindDefaultSprite(${childVarName});`);
            }
        }

        if (this.boundHoverSprite) {
            const childVarName = findChildVarName(this.boundHoverSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定悬停状态精灵 (UIImage)`);
                codes.push(`${indent}${varName}.bindHoverSprite(${childVarName});`);
            }
        }

        if (this.boundPressedSprite) {
            const childVarName = findChildVarName(this.boundPressedSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定按下状态精灵 (UIImage)`);
                codes.push(`${indent}${varName}.bindPressedSprite(${childVarName});`);
            }
        }

        return codes.join('\n');
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIButton 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIButton 特有属性
            enabled: this.enabled,

            // 🔑 UIComponent 属性（安全访问）
            name: this.name || '',

            // 🔑 克隆脚本数组
            componentScripts: this.componentScripts ?
                this.componentScripts.map(script => ({ ...script })) : [],


        };
    }

}

// 注册ButtonModel到基类容器
BaseObjectModel.registerModel('UIButton', ButtonModel);
BaseObjectModel.registerModel('Button', ButtonModel);
